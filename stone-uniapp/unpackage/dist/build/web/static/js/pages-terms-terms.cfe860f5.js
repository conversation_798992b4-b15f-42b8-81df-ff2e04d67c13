(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-terms-terms"],{"2f46":function(t,i,e){"use strict";var n=e("39dd"),v=e.n(n);v.a},"39dd":function(t,i,e){var n=e("a6b1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var v=e("967d").default;v("e9150322",n,!0,{sourceMap:!1,shadowMode:!1})},5510:function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return v})),e.d(i,"a",(function(){}));var n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticStyle:{width:"700rpx",margin:"0 auto"}},[e("v-uni-view",{staticStyle:{"text-align":"center","font-weight":"bold","margin-top":"20rpx"}},[t._v("《会员协议》")]),e("v-uni-view",[t._v("鉴于平台为一家提供在线交易服务的电子商务平台，用户为有意在平台平台上进行交易的个人或实体，双方本着平等自愿、诚实信用的原则，就用户成为平台会员并享受相关服务达成如下协议：")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("一、会员资格与注册")]),e("v-uni-view",[t._v("用户同意遵守本协议及平台平台的所有规则和政策，方可注册成为平台会员。")]),e("v-uni-view",[t._v("用户应提供真实、准确的个人信息或企业信息进行注册，如有变更应及时更新。")]),e("v-uni-view",[t._v("注册成功后，用户将获得会员账号和密码，并对账号安全负全部责任。")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("二、会员权利与服务")]),e("v-uni-view",[t._v("用户有权在平台平台上浏览商品或服务信息、下单购买。")]),e("v-uni-view",[t._v("用户有权享受平台提供的会员专属优惠、积分奖励及促销活动。")]),e("v-uni-view",[t._v("平台提供的在线商品或服务信息应真实、准确，不得有虚假宣传。")]),e("v-uni-view",[t._v("平台有权根据市场情况调整商品或服务的价格和库存。")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("三、交易与支付")]),e("v-uni-view",[t._v("用户下单后，平台应在规定时间内确认订单并安排发货或提供服务。")]),e("v-uni-view",[t._v("用户应按照平台平台的支付方式及时完成支付。")]),e("v-uni-view",[t._v("平台对交易过程及结果的准确性负责，确保交易安全。")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("四、会员信息保护")]),e("v-uni-view",[t._v("平台承诺对用户的个人信息进行严格保密，不向第三方泄露。")]),e("v-uni-view",[t._v("平台应采取必要措施保护用户的交易安全，防止信息泄露和非法使用。")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("五、违约责任")]),e("v-uni-view",[t._v("如用户违反本协议规定，平台有权采取包括但不限于警告、限制服务、终止服务等措施。")]),e("v-uni-view",[t._v("如平台违反本协议规定，应承担相应的法律责任。")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("六、争议解决")]),e("v-uni-view",[t._v("双方因本协议产生的任何争议，应首先通过友好协商解决。")]),e("v-uni-view",[t._v("协商不成的，可向平台所在地人民法院提起诉讼。")]),e("v-uni-view",{staticStyle:{"font-weight":"bold","text-indent":"10rpx"}},[t._v("七、其他条款")]),e("v-uni-view",[t._v("平台有权根据实际情况调整会员资格的条件和要求。")]),e("v-uni-view",[t._v("本协议的解释、适用及争议解决均适用中华人民共和国法律。")]),e("v-uni-view",[t._v("本协议自用户注册成为会员之日起生效，至用户注销会员身份或平台终止服务时终止。")]),e("v-uni-view",[t._v("本协议的修改和更新由平台负责通知用户，用户应定期查看更新内容。")])],1)},v=[]},"82e8":function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;i.default={data:function(){return{}},methods:{}}},"89b7":function(t,i,e){"use strict";e.r(i);var n=e("82e8"),v=e.n(n);for(var u in n)["default"].indexOf(u)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(u);i["default"]=v.a},a6b1:function(t,i,e){var n=e("c86c");i=n(!1),i.push([t.i,"uni-view[data-v-cee922b2]{line-height:%?40?%;text-indent:%?40?%}",""]),t.exports=i},b355:function(t,i,e){"use strict";e.r(i);var n=e("5510"),v=e("89b7");for(var u in v)["default"].indexOf(u)<0&&function(t){e.d(i,t,(function(){return v[t]}))}(u);e("2f46");var o=e("828b"),a=Object(o["a"])(v["default"],n["b"],n["c"],!1,null,"cee922b2",null,!1,n["a"],void 0);i["default"]=a.exports}}]);