(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-register_2"],{"3ece":function(r,t,e){"use strict";e.r(t);var o=e("9075"),s=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(r){e.d(t,r,(function(){return o[r]}))}(a);t["default"]=s.a},"541b":function(r,t,e){var o=e("c86c");t=o(!1),t.push([r.i,".container[data-v-601bb849]{height:86vh;padding:%?20?%;background-color:#f5f5f5}.header[data-v-601bb849]{text-align:center;padding:%?20?%;font-size:%?36?%;color:#333;margin-bottom:%?20?%}.form[data-v-601bb849]{padding:%?20?%;border-radius:%?10?%;box-shadow:0 %?2?% %?5?% rgba(0,0,0,.1)}.input[data-v-601bb849]{width:100%;padding:%?10?%;margin-bottom:%?10?%;border:%?1?% solid #ddd;border-radius:%?5?%;font-size:%?28?%}.error[data-v-601bb849]{color:#f44336;font-size:%?24?%;margin-bottom:%?10?%}.verification[data-v-601bb849]{margin:10px;width:50%}.send-code-btn[data-v-601bb849]{padding:%?10?% %?20?%;background-color:#3cb371;color:#fff;border-radius:10px;font-size:%?28?%}.agreement[data-v-601bb849]{display:flex;align-items:center;margin:10px 0;font-size:%?24?%}.link[data-v-601bb849]{color:#3cb371;margin-left:%?5?%}.submit-btn[data-v-601bb849]{width:100%;background-color:#3cb371;color:#fff;padding:%?15?% 0;border-radius:10px;font-size:%?32?%;text-align:center}.app-download[data-v-601bb849]{width:100%;text-align:center;color:#f57c00;margin-top:%?20?%;font-size:%?28?%;position:fixed;left:0;bottom:15%}.form-group[data-v-601bb849]{margin:%?10?%}.form-group uni-input[data-v-601bb849]{width:90%;border-radius:15px;padding:1vh 2vw}.referrers[data-v-601bb849]{width:100%;height:3vh;line-height:3vh;position:fixed;left:0;bottom:10vh;text-align:center}",""]),r.exports=t},"721d":function(r,t,e){"use strict";var o=e("a02b"),s=e.n(o);s.a},"7acf":function(r,t,e){"use strict";e.r(t);var o=e("e163"),s=e("3ece");for(var a in s)["default"].indexOf(a)<0&&function(r){e.d(t,r,(function(){return s[r]}))}(a);e("721d");var n=e("828b"),i=Object(n["a"])(s["default"],o["b"],o["c"],!1,null,"601bb849",null,!1,o["a"],void 0);t["default"]=i.exports},9075:function(r,t,e){"use strict";e("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("f7a5"),e("5c47"),e("0506"),e("dc8a");var o={data:function(){return{form:{phone:"",name:"",password:"",password_2:"",transactionPassword:"",transactionPassword_2:"",agree:!1},errors:{},cooldown:0,isCooldown:!1,referrers:""}},onShow:function(r){this.bindEvent()},methods:{bindEvent:function(){console.log(window.location);var r=window.location.href;this.referrers=r.slice(-11),console.log(this.referrers)},validateForm:function(){return this.errors={},this.form.phone&&/^1[3-9]\d{9}$/.test(this.form.phone)||(this.errors.phone="请输入正确的手机号"),this.form.code&&6===this.form.code.length||(this.errors.code="请输入6位验证码"),this.form.name||(this.errors.name="请输入真实姓名"),(!this.form.password||this.form.password.length<6)&&(this.errors.password="密码至少6位"),(!this.form.password_2||this.form.password_2.length<6||this.form.password_2!=this.form.password)&&(this.errors.password_2="请确认与密码一致"),(!this.form.transactionPassword||this.form.transactionPassword.length<6)&&(this.errors.transactionPassword="交易密码至少6位"),(!this.form.transactionPassword_2||this.form.transactionPassword_2.length<6||this.form.transactionPassword_2!=this.form.transactionPassword)&&(this.errors.transactionPassword_2="请确认与交易密码一致"),this.form.agree||(this.errors.agree="请阅读并同意相关协议"),0===Object.keys(this.errors).length},sendCode:function(){var r=this;if(this.form.phone&&/^1[3-9]\d{9}$/.test(this.form.phone)){this.errors.phone="",console.log("发送验证码到:",this.form.phone),this.$api.request({url:this.$api.getdecode+"?phone="+this.form.phone}).then((function(r){console.log(r),uni.showToast({title:"验证码已发送",icon:"success"})})),this.cooldown=60,this.isCooldown=!0;var t=setInterval((function(){r.cooldown-=1,r.cooldown<=0&&(clearInterval(t),r.isCooldown=!1)}),1e3)}else this.errors.phone="请输入正确的手机号"},register:function(){console.log("1111"),console.log("提交注册信息:",this.form),this.$api.request({url:this.$api.register_2,method:"POST",data:{password:this.form.password,phone:this.form.phone,realName:this.form.name,transactionPassword:this.form.transactionPassword,referrers:this.referrers}}).then((function(r){console.log(r,"返回");var t=r.appUser,e={id:t.id,headImg:t.headImg,nickName:t.nickName,phone:t.phone,realName:t.realName};uni.setStorageSync("user_count_key",r.token),uni.setStorageSync("userInfo",e),uni.showToast({title:"注册成功"}),setTimeout((function(){location.href="http://116.204.89.181:83"}),1200)}))},viewAgreement:function(r){var t="";"terms"===r&&(t="/pages/terms/terms"),"privacy"===r&&(t="/pages/privacy/privacy"),uni.navigateTo({url:t})},checked:function(r){this.form.agree=!this.form.agree}}};t.default=o},a02b:function(r,t,e){var o=e("541b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[r.i,o,""]]),o.locals&&(r.exports=o.locals);var s=e("967d").default;s("7b2baa3d",o,!0,{sourceMap:!1,shadowMode:!1})},e163:function(r,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return s})),e.d(t,"a",(function(){}));var o=function(){var r=this,t=r.$createElement,e=r._self._c||t;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"form"},[e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",[r._v("真实姓名："),r.errors.name?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.name))]):r._e()],1),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"真实姓名，注册后不能修改"},model:{value:r.form.name,callback:function(t){r.$set(r.form,"name",t)},expression:"form.name"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",[r._v("手机号："),r.errors.phone?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.phone))]):r._e()],1),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"真实手机号码",maxlength:"11",type:"number"},model:{value:r.form.phone,callback:function(t){r.$set(r.form,"phone",t)},expression:"form.phone"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",[r._v("登陆密码："),r.errors.password?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.password))]):r._e()],1),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"登录密码",type:"password"},model:{value:r.form.password,callback:function(t){r.$set(r.form,"password",t)},expression:"form.password"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",[r._v("确认密码："),r.errors.password_2?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.password_2))]):r._e()],1),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"确认密码",type:"password"},model:{value:r.form.password_2,callback:function(t){r.$set(r.form,"password_2",t)},expression:"form.password_2"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",[r._v("交易密码："),r.errors.transactionPassword?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.transactionPassword))]):r._e()],1),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"交易密码",type:"password"},model:{value:r.form.transactionPassword,callback:function(t){r.$set(r.form,"transactionPassword",t)},expression:"form.transactionPassword"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",[r._v("确认交易密码："),r.errors.transactionPassword_2?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.transactionPassword_2))]):r._e()],1),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"确认交易密码",type:"password"},model:{value:r.form.transactionPassword_2,callback:function(t){r.$set(r.form,"transactionPassword_2",t)},expression:"form.transactionPassword_2"}})],1),e("v-uni-view",{staticClass:"form-group"},[e("v-uni-view",{staticClass:"agreement"},[e("v-uni-checkbox",{on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.checked.apply(void 0,arguments)}},model:{value:r.form.agree,callback:function(t){r.$set(r.form,"agree",t)},expression:"form.agree"}}),e("v-uni-text",[r._v("已阅读并同意")]),e("v-uni-text",{staticClass:"link",on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.viewAgreement("terms")}}},[r._v("《会员协议》")]),e("v-uni-text",[r._v("和")]),e("v-uni-text",{staticClass:"link",on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.viewAgreement("privacy")}}},[r._v("《隐私政策》")])],1),r.errors.agree?e("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.agree))]):r._e()],1),e("v-uni-button",{staticClass:"submit-btn",on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.register.apply(void 0,arguments)}}},[r._v("确定注册")]),""!=r.referrers?e("v-uni-view",{staticClass:"referrers"},[r._v("推荐人手机号:"+r._s(r.referrers))]):r._e()],1)],1)},s=[]}}]);