(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-myPage-sec_page-realname"],{1851:function(t,e,n){"use strict";var i=n("8bdb"),a=n("84d6"),r=n("1cb5");i({target:"Array",proto:!0},{fill:a}),r("fill")},"1ac2":function(t,e,n){"use strict";n.r(e);var i=n("5a97"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"24aa":function(t,e,n){var i=n("33d0");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("1736419c",i,!0,{sourceMap:!1,shadowMode:!1})},"2a29":function(t,e,n){"use strict";var i=n("24aa"),a=n.n(i);a.a},"2deb":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.show?n("v-uni-view",{ref:"limeSignature",staticClass:"lime-signature",style:[t.canvasStyle,t.styles]},[t.useCanvas2d?n("v-uni-canvas",{staticClass:"lime-signature__canvas",attrs:{id:t.canvasId,type:"2d",disableScroll:t.disableScroll},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}}):n("v-uni-canvas",{staticClass:"lime-signature__canvas",attrs:{disableScroll:t.disableScroll,"canvas-id":t.canvasId,id:t.canvasId,width:t.canvasWidth,height:t.canvasHeight},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)},mousedown:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},mousemove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},mouseup:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}}),t.showOffscreen?n("v-uni-canvas",{staticClass:"offscreen",style:"width:"+t.offscreenSize[0]+"px;height:"+t.offscreenSize[1]+"px",attrs:{"canvas-id":"offscreen",id:"offscreen",width:t.offscreenSize[0],height:t.offscreenSize[1]}}):t._e(),t.showMask?n("v-uni-view",{staticClass:"mask",on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}}):t._e()],1):t._e()},a=[]},"33d0":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.lime-signature[data-v-3a035048],\n.lime-signature__canvas[data-v-3a035048]{position:relative;width:100%;height:100%}.mask[data-v-3a035048]{position:absolute;left:0;right:0;bottom:0;top:0}.offscreen[data-v-3a035048]{position:fixed;top:0;pointer-events:none;left:9999px}',""]),t.exports=e},"45cc":function(t,e,n){"use strict";n.r(e);var i=n("61ca"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"48a6":function(t,e,n){"use strict";var i=n("95e1"),a=n.n(i);a.a},"5a97":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("9b1b")),r=i(n("5de6")),o=i(n("2634")),s=i(n("2fdc"));n("d4b5"),n("5c47"),n("0506"),n("fd3c");var u=n("a54f"),c=n("6751"),h=n("6c3e"),l=i(n("bb48")),f=(n("9b8e"),{props:l.default,data:function(){return{canvasWidth:null,canvasHeight:null,offscreenWidth:null,offscreenHeight:null,useCanvas2d:!0,show:!0,offscreenStyles:"",showMask:!1,showOffscreen:!1,isPC:!1}},computed:{canvasId:function(){return"lime-signature".concat(this._uid)},offscreenId:function(){return this.canvasId+"offscreen"},offscreenSize:function(){var t=this.offscreenWidth,e=this.offscreenHeight;return this.landscape?[e,t]:[t,e]},canvasStyle:function(){var t=this.canvasWidth,e=this.canvasHeight,n=this.backgroundColor;return{width:t&&t+"px",height:e&&e+"px",background:n}},param:function(){var t=this.penColor,e=this.penSize,n=this.backgroundColor,i=this.backgroundImage,a=this.landscape,r=this.boundingBox,o=this.openSmooth,s=this.minLineWidth,u=this.maxLineWidth,c=this.minSpeed,h=this.maxWidthDiffRate,l=this.maxHistoryLength,f=this.disableScroll,d=this.disabled;return JSON.parse(JSON.stringify({penColor:t,penSize:e,backgroundColor:n,backgroundImage:i,landscape:a,boundingBox:r,openSmooth:o,minLineWidth:s,maxLineWidth:u,minSpeed:c,maxWidthDiffRate:h,maxHistoryLength:l,disableScroll:f,disabled:d}))}},created:function(){var t=uni.getSystemInfoSync(),e=t.platform;this.isPC=/windows|mac/.test(e),this.useCanvas2d="2d"==this.type&&(0,u.canIUseCanvas2d)()&&!this.isPC},mounted:function(){var t=this;return(0,s.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.beforeDelay){e.next=3;break}return e.next=3,(0,u.sleep)(t.beforeDelay);case 3:return e.next=5,t.getContext();case 5:n=e.sent,t.signature=new c.Signature(n),t.canvasEl=t.signature.canvas.get("el"),t.offscreenWidth=t.canvasWidth=t.signature.canvas.get("width"),t.offscreenHeight=t.canvasHeight=t.signature.canvas.get("height"),t.stopWatch=t.$watch("param",(function(e){t.signature.pen.setOption(e)}),{immediate:!0});case 11:case"end":return e.stop()}}),e)})))()},beforeDestroy:function(){this.stopWatch&&this.stopWatch(),this.signature.destroy(),this.show=!1,this.signature=null},methods:{redo:function(){this.signature&&this.signature.redo()},restore:function(){this.redo()},undo:function(){this.signature&&this.signature.undo()},clear:function(){this.signature&&this.signature.clear()},isEmpty:function(){return this.signature.isEmpty()},canvasToMaskPath:function(){var t=arguments,e=this;return(0,s.default)((0,o.default)().mark((function n(){var i,a,r,s,c,l,f,d,v,p;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=t.length>0&&void 0!==t[0]?t[0]:{},a=e.isEmpty(),e.showOffscreen=!0,r=e.signature.canvas.get("width"),s=e.signature.canvas.get("height"),c=uni.getSystemInfoSync(),l=c.pixelRatio,e.useCanvas2d?(e.offscreenWidth=r*l,e.offscreenHeight=s*l):(e.offscreenWidth=r,e.offscreenHeight=s),n.next=9,(0,u.sleep)(100);case 9:f=uni.createCanvasContext("offscreen",e),d=Math.max(e.offscreenWidth,e.offscreenHeight),v=function(t){return i.success&&i.success(t)},p=function(t){return i.fail&&i.fail(t)},e.signature.pen.getMaskedImageData((function(t){(function(t,e){uni.canvasPutImageData?uni.canvasPutImageData(t,e):f.putImageData&&f.putImageData(t)})({canvasId:"offscreen",x:0,y:0,width:r,height:s,data:t,fail:function(t){p(t)},success:function(t){(0,h.toDataURL)("offscreen",e,i).then((function(t){f.restore(),f.clearRect(0,0,d,d),e.offscreenWidth=r,e.offscreenHeight=s,e.showOffscreen=!1,v({tempFilePath:t,isEmpty:a})}))}},e)}));case 14:case"end":return n.stop()}}),n)})))()},canvasToTempFilePath:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.isEmpty(),i=this.useCanvas2d,a=function(t){return e.success&&e.success(t)},c=function(t){return e.fail&&e.fail(t)},l=this.signature.canvas.get("el"),f=l.canvas,d=this.backgroundColor,v=this.landscape,p=this.boundingBox,g=this.signature.canvas.get("width"),m=this.signature.canvas.get("height"),y=0,w=0,x="devtools"==uni.getSystemInfoSync().platform,b=this.preferToDataURL,S=1,_=function(){var c=(0,s.default)((0,o.default)().mark((function s(c){var l,y,w,_,k,E;return(0,o.default)().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(l=function(){var e=i&&!!uni.createOffscreenCanvas&&b;if(e&&!x){var n=uni.createOffscreenCanvas({type:"2d"});n.width=t.offscreenSize[0]*S,n.height=t.offscreenSize[1]*S;var a=n.getContext("2d");return[a,n]}var r=uni.createCanvasContext("offscreen",t);return[r]},!(p&&!t.isPC||v||d&&!(0,u.isTransparent)(d))){o.next=13;break}return t.showOffscreen=!0,o.next=5,(0,u.sleep)(100);case 5:y=l(),w=(0,r.default)(y,2),_=w[0],k=w[1],_.save(),_.setTransform(1,0,0,1,0,0),v&&(_.translate(0,g*S),_.rotate(-Math.PI/2)),d&&!(0,u.isTransparent)(d)&&(_.fillStyle=d,_.fillRect(0,0,g,m)),k?(E=f.createImage(),E.src=c,E.onload=function(){_.drawImage(E,0,0,g*S,m*S);var e=k.toDataURL();t.showOffscreen=!1,a({tempFilePath:e,isEmpty:n})}):(_.drawImage(c,0,0,g*S,m*S),_.draw(!1,(function(){(0,h.toDataURL)("offscreen",t,e).then((function(e){var i=Math.max(g,m);_.restore(),_.clearRect(0,0,i,i),t.showOffscreen=!1,a({tempFilePath:e,isEmpty:n})}))}))),o.next=14;break;case 13:a({tempFilePath:c,isEmpty:n});case 14:case"end":return o.stop()}}),s)})));return function(t){return c.apply(this,arguments)}}(),k=function(){var e=(0,s.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.offscreenWidth==g&&t.offscreenHeight==m){e.next=5;break}return t.offscreenWidth=g,t.offscreenHeight=m,e.next=5,(0,u.sleep)(100);case 5:n={x:y,y:w,width:g,height:m,canvas:f,preferToDataURL:b},(0,h.toDataURL)(t.canvasId,t,n).then(_).catch(c);case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();p&&!this.isPC?this.signature.getContentBoundingBox(function(){var e=(0,s.default)((0,o.default)().mark((function e(n){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.offscreenWidth=g=n.width,t.offscreenHeight=m=n.height,y=n.startX,w=n.startY,k();case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()):k()},getContext:function(){var t=this;return(0,u.getRect)("#".concat(this.canvasId),{context:this,type:this.useCanvas2d?"fields":"boundingClientRect"}).then((function(e){if(e){var n,i=e.width,a=e.height,r=e.node,o=e.left,s=e.top,c=e.right,l=uni.getSystemInfoSync(),f=l.pixelRatio;return r?(n=r.getContext("2d"),r.width=i*f,r.height=a*f):(f=1,n=(0,h.uniContext)(t.canvasId,t),r={getContext:function(t){return"2d"==t?n:null},createImage:h.createImage,toDataURL:function(){return(0,h.toDataURL)(t.canvasId,t)},requestAnimationFrame:u.requestAnimationFrame}),n.clearRect(0,0,i,a),{left:o,top:s,right:c,width:i,height:a,context:n,canvas:r,pixelRatio:f}}}))},getTouch:function(t){var e=this;return this.isPC&&this.canvasRect&&(t.touches=t.touches.map((function(t){return(0,a.default)((0,a.default)({},t),{},{x:t.clientX-e.canvasRect.left,y:t.clientY-e.canvasRect.top})}))),t},touchStart:function(t){var e=this;this.canvasEl&&(this.isStart=!0,this.isPC?(0,u.getRect)("#".concat(this.canvasId),{context:this}).then((function(n){e.canvasRect=n,e.canvasEl.dispatchEvent("touchstart",(0,u.wrapEvent)(e.getTouch(t)))})):this.canvasEl.dispatchEvent("touchstart",(0,u.wrapEvent)(t)))},touchMove:function(t){!this.canvasEl||!this.isStart&&this.canvasEl||this.canvasEl.dispatchEvent("touchmove",(0,u.wrapEvent)(this.getTouch(t)))},touchEnd:function(t){this.canvasEl&&(this.isStart=!1,this.canvasEl.dispatchEvent("touchend",(0,u.wrapEvent)(t)))}}});e.default=f},"61ca":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{title:"Hello",penColor:"black",penSize:5,fileUrl:"",openSmooth:!0,imgurl:""}},onLoad:function(){this.api_get(uni.getStorageSync("userInfo").id),console.log(this.imgurl)},methods:{api_get:function(t){var e=this;this.$api.request({url:this.$api.getsignnam+"?id="+t}).then((function(t){console.log(t),e.imgurl=t.data.nameAuth}))},onClick:function(t){var e=this;"openSmooth"!=t?"save"!=t?this.$refs.signatureRef&&this.$refs.signatureRef[t]():this.$refs.signatureRef.canvasToTempFilePath({success:function(t){e.fileUrl=t.tempFilePath,uni.uploadFile({url:e.$api.upPic_2,filePath:e.fileUrl,name:"file",formData:{user:"test"},success:function(t){var n=JSON.parse(t.data);console.log(n),e.$api.request({url:e.$api.saveSign,method:"POST",data:{id:uni.getStorageSync("userInfo").id,nameAuth:n.url}}).then((function(t){200==t.code&&(e.imgurl=n.url,uni.showToast({title:"保存成功"}))}))}})}}):this.openSmooth=!this.openSmooth}}};e.default=i},6751:function(t,e,n){"use strict";var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Signature=void 0;var a=i(n("fcf3"));function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function o(t){for(var e=1;arguments.length>e;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function s(t){return s="function"==typeof Symbol&&"symbol"==(0,a.default)(Symbol.iterator)?function(t){return(0,a.default)(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":(0,a.default)(t)},s(t)}function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){for(var n=0;e.length>n;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function h(t,e,n){return e&&c(t.prototype,e),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function l(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}function d(t){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},d(t)}function v(t,e){return v=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},v(t,e)}function p(t,e){if(e&&("object"==(0,a.default)(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function g(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=d(t);if(e){var a=d(this).constructor;n=Reflect.construct(i,arguments,a)}else n=i.apply(this,arguments);return p(this,n)}}n("dc8a"),n("01a2"),n("8f71"),n("bf0f"),n("9a2c"),n("aa9c"),n("2797"),n("a644"),n("a03a"),n("6a54"),n("e39c"),n("844d"),n("18f7"),n("de6c"),n("7a76"),n("c9b5"),n("8a8d"),n("926e"),n("7996"),n("6a88"),n("dd2b"),n("e838"),n("64aa"),n("1851"),n("f7a5");var m=function(t){var e=s(t);return null!==t&&"object"===e||"function"===e},y={}.toString,w=function(t,e){return y.call(t)==="[object "+e+"]"},x=function(t){return w(t,"String")},b=function(t){return w(t,"Number")},S=function(t){return w(t,"Function")},_=function(){function t(){u(this,t),this.__events=void 0,this.__events={}}return h(t,[{key:"on",value:function(t,e){if(t&&e){var n=this.__events[t]||[];n.push(e),this.__events[t]=n}}},{key:"emit",value:function(t,e){var n=this;if(m(t)&&(t=(e=t)&&e.type),t){var i=this.__events[t];i&&i.length&&i.forEach((function(t){t.call(n,e)}))}}},{key:"off",value:function(t,e){var n=this.__events,i=n[t];if(i&&i.length)if(e)for(var a=0,r=i.length;r>a;a++)i[a]===e&&(i.splice(a,1),a--);else delete n[t]}},{key:"getEvents",value:function(){return this.__events}}]),t}(),k=function(t){f(n,_);var e=g(n);function n(t,i){var a;return u(this,n),(a=e.call(this)).context=void 0,a.canvas=void 0,a.attrs=void 0,a.isCanvasElement=void 0,a.context=t,a.canvas=i.canvas||t.canvas||{width:i.width||0,height:i.height||0},a.attrs=i||{},a.isCanvasElement=!0,a}return h(n,[{key:"width",get:function(){return this.canvas.width},set:function(t){this.canvas.width=t}},{key:"height",get:function(){return this.canvas.height},set:function(t){this.canvas.height=t}},{key:"getContext",value:function(){return this.context}},{key:"getBoundingClientRect",value:function(){var t=this.attrs||{},e=t.top,n=t.right,i=t.width,a=t.height,r=t.left,o=t.bottom;return{top:void 0===e?0:e,width:void 0===i?0:i,right:void 0===n?0:n,height:void 0===a?0:a,bottom:void 0===o?0:o,left:void 0===r?0:r}}},{key:"setAttribute",value:function(t,e){this.attrs[t]=e}},{key:"addEventListener",value:function(t,e){this.on(t,e)}},{key:"removeEventListener",value:function(t,e){this.off(t,e)}},{key:"dispatchEvent",value:function(t,e){this.emit(t,e)}}]),n}();function E(t,e){try{return t.currentStyle?t.currentStyle[e]:document.defaultView&&document.defaultView.getComputedStyle(t,null).getPropertyValue(e)}catch(t){return{width:300,height:150}[e]}}function P(t,e){var n=e.get("el");if(!n)return t;var i=n.getBoundingClientRect(),a=i.top,r=void 0===a?0:a,o=i.left,s=void 0===o?0:o,u=parseFloat(E(n,"padding-left"))||0,c=parseFloat(E(n,"padding-top"))||0;return{x:t.x-s-u,y:t.y-r-c}}function C(t,e){var n=e.get("landscape");if(!n)return t;if(S(n))return n(t,e);var i=e.get("height");return{x:t.y,y:i-t.x}}var I=function(t,e){var n=t.touches;if(!n||!n.length)return[C(P({x:t.clientX,y:t.clientY},e),e)];n.length||(n=t.changedTouches||[]);for(var i=[],a=0,r=n.length;r>a;a++){var o,s=n[a],u=s.x,c=s.y,h=s.clientX,l=s.clientY;o=b(u)||b(c)?{x:u,y:c}:P({x:h,y:l},e),i.push(C(o,e))}return i},M=function(t,e){var n=e.x-t.x,i=e.y-t.y;return Math.abs(n)>Math.abs(i)?n>0?"right":"left":i>0?"down":"up"},D=function(t,e){var n=Math.abs(e.x-t.x),i=Math.abs(e.y-t.y);return Math.sqrt(n*n+i*i)},L=function(){function t(e){var n=this,i=e.canvas,a=e.el;u(this,t),this.processEvent=void 0,this.canvas=void 0,this.startTime=0,this.endTime=0,this.startPoints=null,this.startDistance=0,this.center=null,this.pressTimeout=void 0,this.eventType=null,this.direction=null,this.lastMoveTime=0,this.prevMovePoints=null,this.prevMoveTime=0,this.lastMovePoints=null,this.pinch=!1,this._click=function(t){var e=I(t,n.canvas);t.points=e,n.emitEvent("click",t)},this._start=function(t){var e,i,a=I(t,n.canvas);a&&(t.points=a,n.emitEvent("touchstart",t),n.reset(),n.startTime=Date.now(),n.startPoints=a,a.length>1?(n.startDistance=D(a[0],a[1]),n.center={x:(e=a[0]).x+((i=a[1]).x-e.x)/2,y:e.y+(i.y-e.y)/2}):n.pressTimeout=setTimeout((function(){var e="press",i="none";t.direction=i,n.emitStart(e,t),n.emitEvent(e,t),n.eventType=e,n.direction=i}),250))},this._move=function(t){var e=I(t,n.canvas);if(e){t.points=e,n.emitEvent("touchmove",t);var i=n.startPoints;if(i)if(e.length>1){var a=n.startDistance,r=D(e[0],e[1]);t.zoom=r/a,t.center=n.center,n.emitStart("pinch",t),n.emitEvent("pinch",t)}else{var o=e[0].x-i[0].x,s=e[0].y-i[0].y,u=n.direction||M(i[0],e[0]);n.direction=u;var c=n.getEventType(e);t.direction=u,t.deltaX=o,t.deltaY=s,n.emitStart(c,t),n.emitEvent(c,t);var h=n.lastMoveTime,l=Date.now();l-h>0&&(n.prevMoveTime=h,n.prevMovePoints=n.lastMovePoints,n.lastMoveTime=l,n.lastMovePoints=e)}}},this._end=function(t){var e=I(t,n.canvas);t.points=e,n.emitEnd(t),n.emitEvent("touchend",t);var i=n.lastMoveTime;if(100>Date.now()-i){var a=i-(n.prevMoveTime||n.startTime);if(a>0){var r=n.prevMovePoints||n.startPoints,o=n.lastMovePoints;if(!r||!o)return;var s=D(r[0],o[0])/a;s>.3&&(t.velocity=s,t.direction=M(r[0],o[0]),n.emitEvent("swipe",t))}}n.reset();var u=t.touches;u&&u.length>0&&n._start(t)},this._cancel=function(t){n.emitEvent("touchcancel",t),n.reset()},this.canvas=i,this.delegateEvent(a),this.processEvent={}}return h(t,[{key:"delegateEvent",value:function(t){t.addEventListener("click",this._click),t.addEventListener("touchstart",this._start),t.addEventListener("touchmove",this._move),t.addEventListener("touchend",this._end),t.addEventListener("touchcancel",this._cancel)}},{key:"emitEvent",value:function(t,e){this.canvas.emit(t,e)}},{key:"getEventType",value:function(t){var e,n=this.eventType,i=this.startTime,a=this.startPoints;if(n)return n;var r=this.canvas.__events.pan;if(r&&r.length){var o=Date.now();if(!a)return;e=o-i>250&&10>D(a[0],t[0])?"press":"pan"}else e="press";return this.eventType=e,e}},{key:"enable",value:function(t){this.processEvent[t]=!0}},{key:"isProcess",value:function(t){return this.processEvent[t]}},{key:"emitStart",value:function(t,e){this.isProcess(t)||(this.enable(t),this.emitEvent("".concat(t,"start"),e))}},{key:"emitEnd",value:function(t){}},{key:"clearPressTimeout",value:function(){this.pressTimeout&&(clearTimeout(this.pressTimeout),this.pressTimeout=null)}},{key:"reset",value:function(){this.clearPressTimeout(),this.startTime=0,this.startPoints=null,this.startDistance=0,this.direction=null,this.eventType=null,this.pinch=!1,this.prevMoveTime=0,this.prevMovePoints=null,this.lastMoveTime=0,this.lastMovePoints=null}}]),t}(),T=function(t){f(n,_);var e=g(n);function n(t){var i;return u(this,n),(i=e.call(this))._attrs={},i._isWindow=void 0,i._attrs=Object.assign({},t),i._isWindow="undefined"!=typeof window,i._initPixelRatio(),i._initCanvas(),["createImage","toDataURL","requestAnimationFrame"].forEach((function(e){i._initAttrs(e,t.canvas||i.get("el"))})),i}return h(n,[{key:"get",value:function(t){return this._attrs[t]}},{key:"set",value:function(t,e){this._attrs[t]=e}},{key:"_initAttrs",value:function(t,e){var n=this;this.get(t)||this.set(t,(function(){return e[t]?e[t].apply(e,arguments):n._isWindow?window[t]?(i=window)[t].apply(i,arguments):"createImage"==t?new Image:null:void 0;var i}))}},{key:"_initCanvas",value:function(){var t,e,n=this.get("el"),i=this.get("context");if(!n&&!i)throw Error("请指定 id、el 或 context!");t=n?x(n)?(e=n)?document.getElementById(e):null:n:function(t,e){return t?function(t){if(!t)return!1;if(1!==t.nodeType||!t.nodeName||"canvas"!==t.nodeName.toLowerCase())return!1;var e=!1;try{t.addEventListener("eventTest",(function(){e=!0})),t.dispatchEvent(new Event("eventTest"))}catch(t){e=!1}return e}(t.canvas)?t.canvas:new k(t,e):null}(i,this._attrs),i&&t&&!t.getContext&&(t.getContext=function(){return i});var a=this.get("width")||function(t){var e=E(t,"width");return"auto"===e&&(e=t.offsetWidth),parseFloat(e)}(t)||t.width,r=this.get("height")||function(t){var e=E(t,"height");return"auto"===e&&(e=t.offsetHeight),parseFloat(e)}(t)||t.height;this.set("canvas",this),this.set("el",t),this.set("context",i||t.getContext("2d")),this.changeSize(a,r);var o=new L({canvas:this,el:t,parent:this.get("parent")});this.set("eventController",o)}},{key:"_initPixelRatio",value:function(){this.get("pixelRatio")||this.set("pixelRatio",window&&window.devicePixelRatio||1)}},{key:"changeSize",value:function(t,e){var n,i=this.get("pixelRatio"),a=this.get("el");a.style&&(a.style.width=t+"px",a.style.height=e+"px"),(n=a)&&"object"===s(n)&&(1===n.nodeType&&n.nodeName||n.isCanvasElement)&&(a.width=t*i,a.height=e*i,1!==i&&this.get("context").scale(i,i)),this.set("width",t),this.set("height",e)}},{key:"destroy",value:function(){if(!this.get("destroyed")){var t=this.get("el");t.width=0,t.height=0,this.clear(),this._attrs={},this.set("destroyed",!0)}}},{key:"clear",value:function(){}},{key:"isDestroyed",value:function(){return this.get("destroyed")}}]),n}(),R={penColor:"black",backgroundColor:"",openSmooth:!0,penSize:2,minLineWidth:2,maxLineWidth:6,minSpeed:1.5,maxWidthDiffRate:20,maxHistoryLength:20},O=null,W=function(){function t(e){var n=this;u(this,t),this.canAddHistory=!0,this.points=[],this.historyList=[],this.undoneList=[],this.canvas=void 0,this._isEmpty=!0,this.active=!1,this.getLineWidth=function(t){var e=n.get("options"),i=e.minSpeed,a=e.minLineWidth,r=n.getMaxLineWidth();return Math.min(Math.max(r-(r-a)*t/Math.max(Math.min(i,10),1),a),r)},this.drawTrapezoid=function(t,e,i,a){var r=n.get("context");r.beginPath(),r.moveTo(Number(t.x.toFixed(1)),Number(t.y.toFixed(1))),r.lineTo(Number(e.x.toFixed(1)),Number(e.y.toFixed(1))),r.lineTo(Number(i.x.toFixed(1)),Number(i.y.toFixed(1))),r.lineTo(Number(a.x.toFixed(1)),Number(a.y.toFixed(1))),r.fillStyle=n.get("options").penColor,r.fill(),r.draw&&r.draw(!0)},this.drawNoSmoothLine=function(t,e){e.lastX=t.x+.5*(e.x-t.x),e.lastY=t.y+.5*(e.y-t.y),"number"==typeof t.lastX&&n.drawCurveLine(t.lastX,t.lastY,t.x,t.y,e.lastX,e.lastY,n.getMaxLineWidth())},this.drawCurveLine=function(t,e,i,a,r,o,s){s=Number(s.toFixed(1));var u=n.get("context");u.lineWidth=s,u.beginPath(),u.moveTo(Number(t.toFixed(1)),Number(e.toFixed(1))),u.quadraticCurveTo(Number(i.toFixed(1)),Number(a.toFixed(1)),Number(r.toFixed(1)),Number(o.toFixed(1))),u.stroke(),u.draw&&u.draw(!0)},this.getRadianData=function(t,e,n,i){var a=n-t,r=i-e;if(0===a)return{val:0,pos:-1};if(0===r)return{val:0,pos:1};var o=Math.abs(Math.atan(r/a));return n>t&&e>i||t>n&&i>e?{val:o,pos:1}:{val:o,pos:-1}},this.getRadianPoints=function(t,e,n,i){if(0===t.val)return 1===t.pos?[{x:e,y:n+i},{x:e,y:n-i}]:[{y:n,x:e+i},{y:n,x:e-i}];var a=Math.sin(t.val)*i,r=Math.cos(t.val)*i;return 1===t.pos?[{x:e+a,y:n+r},{x:e-a,y:n-r}]:[{x:e+a,y:n-r},{x:e-a,y:n+r}]},this.drawSmoothLine=function(t,e){var i=e.x-t.x,a=e.y-t.y;if(Math.abs(i)+Math.abs(a)>2?(e.lastX1=t.x+.3*i,e.lastY1=t.y+.3*a,e.lastX2=t.x+.7*i,e.lastY2=t.y+.7*a):(e.lastX1=e.lastX2=t.x+.5*i,e.lastY1=e.lastY2=t.y+.5*a),e.perLineWidth=(t.lineWidth+e.lineWidth)/2,"number"==typeof t.lastX1){if(n.drawCurveLine(t.lastX2,t.lastY2,t.x,t.y,e.lastX1,e.lastY1,e.perLineWidth),t.isFirstPoint)return;if(t.lastX1===t.lastX2&&t.lastY1===t.lastY2)return;var r=n.getRadianData(t.lastX1,t.lastY1,t.lastX2,t.lastY2),o=n.getRadianPoints(r,t.lastX1,t.lastY1,t.perLineWidth/2),s=n.getRadianPoints(r,t.lastX2,t.lastY2,e.perLineWidth/2);n.drawTrapezoid(o[0],s[0],s[1],o[1])}else e.isFirstPoint=!0},this.addHistory=function(){var t=n.get("options").maxHistoryLength;if(t&&n.canAddHistory)if(n.canAddHistory=!1,n.get("createImage")){var e=null;e=n.get("createImage")();var i=n.get("toDataURL")&&n.get("toDataURL")();x(i)?e.src=i:i.then((function(t){e.src=t})),e.onload=function(){var i=O;O=e,n.historyList.push(i),n.historyList=n.historyList.slice(-t)}}else n.historyList.length++},this.drawByImage=function(t){var e=n.get("context"),i=n.get("width"),a=n.get("height");e.clearRect(0,0,i,a);try{t&&e.drawImage(t,0,0,i,a),e.draw&&e.draw(!0)}catch(t){n.historyList.length=0}},this.isEmpty=function(){return n.get("options").maxHistoryLength>0?0===n.historyList.length:n._isEmpty},this.clear=function(){if(!n.get("options").disabled){var t=n.get("context");t.clearRect(0,0,n.get("width"),n.get("height")),t.draw&&t.draw(),n._isEmpty=!0,O=null,n.historyList.length=0}},this.undo=function(){if(!n.get("options").disabled&&(0===n.get("options").maxHistoryLength&&n.clear(),n.get("createImage")&&n.historyList.length)){var t=n.historyList.pop();n.drawByImage(t),n.undoneList.push(O),O=t,n.historyList.length||n.undoneList.length||n.clear()}},this.redo=function(){if(n.undoneList.length&&!n.get("options").disabled){var t=n.undoneList.pop();n.historyList.push(O),n.drawByImage(t),O=t,n._isEmpty=!1}},this.canvas=e,this.canvas.set("pen",R),this.init()}return h(t,[{key:"getOption",value:function(){}},{key:"setOption",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=o({},t),n=e.maxLineWidth;if(n&&t.penSize&&n==R.maxLineWidth){var i=Math.max(n,t.penSize);e.maxLineWidth=i}this.canvas.set("pen",Object.assign({},R,e))}},{key:"get",value:function(t){return this.canvas.get("options"==t?"pen":t)}},{key:"init",value:function(){var t=this;this.get("context").lineCap="round",this.canvas.on("touchstart",(function(e){return t.onDrawStart(e)})),this.canvas.on("touchmove",(function(e){return t.onDrawMove(e)})),this.canvas.on("touchend",(function(e){return t.onDrawEnd(e)}))}},{key:"drawBackground",value:function(){var t=this.get("context"),e=this.get("width"),n=this.get("height"),i=this.get("options"),a=i.backgroundColor,r=i.backgroundImage;a&&(t.fillStyle=a,t.fillRect(0,0,e,n),t.draw&&t.draw(!0)),r&&this.drawByImage(r)}},{key:"getImageData",value:function(t){if(t){var e=this.get("width"),n=this.get("height"),i=this.get("el"),a="CANVAS"===i.nodeName,r=a?e:i.width,o=a?n:i.height;if(a){var s=document.createElement("canvas");s.width=e,s.height=n;var u=s.getContext("2d");u.drawImage(i,0,0,e,n);var c=u.getImageData(0,0,e,n).data;return t(c)}var h,l=this.get("context").getImageData(0,0,r,o);return m(h=l)&&S(h.then)&&S(h.catch)?(l.then((function(e){return t(e.data)})),null):t(l.data)}}},{key:"getMaskedImageData",value:function(t){if(t)return this.getImageData((function(e){for(var n=0;e.length>n;n+=4)0===e[n+3]?(e[n]=0,e[n+1]=0,e[n+2]=0):(e[n]=255,e[n+1]=255,e[n+2]=255);return t(e)}))}},{key:"getContentBoundingBox",value:function(t){var e=this.get("pixelRatio"),n=this.get("width"),i=this.get("height"),a=this.get("el"),r="CANVAS"===a.nodeName,o=r?n:a.width,s=r?i:a.height;return e=r?1:e,this.getImageData((function(n){for(var i=Math.floor(o),a=i,r=Math.floor(s),u=0,c=0,h=0;n.length>h;h+=4)if(n[h+3]>0){var l=h/4%i,f=Math.floor(h/4/i);a=Math.min(a,l),r=Math.min(r,f),u=Math.max(u,l),c=Math.max(c,f)}var d={width:(u-a+1)/e,height:(c-r+1)/e,startX:a/e,startY:r/e};return t&&t(d),d}))}},{key:"remove",value:function(){var t=this;this.canvas.off("touchstart",(function(e){return t.onDrawStart(e)})),this.canvas.off("touchmove",(function(e){return t.onDrawMove(e)})),this.canvas.off("touchend",(function(e){return t.onDrawEnd(e)}))}},{key:"disableScroll",value:function(t){t.preventDefault&&this.get("options").disableScroll&&t.preventDefault()}},{key:"onDrawStart",value:function(t){if(!this.get("options").disabled){this.disableScroll(t),this.undoneList.length=0;var e=t.points;if(this.active){this.canAddHistory=!0,this.get("context").strokeStyle=this.get("options").penColor;var n=e[0];this.initPoint(n.x,n.y)}}}},{key:"onDrawMove",value:function(t){if(!this.get("options").disabled&&(this.disableScroll(t),this.active)){var e=t.points[0];this.initPoint(e.x,e.y),this.onDraw()}}},{key:"onDrawEnd",value:function(t){this.active&&!this.get("options").disabled&&(this.addHistory(),this.canAddHistory=!0,this.points=[])}},{key:"onDraw",value:function(){var t=this,e=this.get("context");if(this.points.length>=2){e.lineWidth=this.get("options").penSize||2;var n=this.points.slice(-1)[0],i=this.points.slice(-2,-1)[0];(function(){t._isEmpty=!1,t.get("options").openSmooth?t.drawSmoothLine(i,n):t.drawNoSmoothLine(i,n)})()}}},{key:"getMaxLineWidth",value:function(){var t=this.get("options");return Math.min(t.penSize,t.maxLineWidth)}},{key:"initPoint",value:function(t,e){var n={x:t,y:e,t:Date.now()},i=this.points.slice(-1)[0];if(!i||i.t!==n.t&&(i.x!==t||i.y!==e)){if(this.get("options").openSmooth&&i){var a=this.points.slice(-2,-1)[0];if(n.distance=Math.sqrt(Math.pow(n.x-i.x,2)+Math.pow(n.y-i.y,2)),n.speed=n.distance/(n.t-i.t||.1),n.lineWidth=this.getLineWidth(n.speed),a&&a.lineWidth&&i.lineWidth){var r=(n.lineWidth-i.lineWidth)/i.lineWidth,o=this.get("options").maxWidthDiffRate/100;o=o>1?1:.01>o?.01:o,Math.abs(r)>o&&(n.lineWidth=i.lineWidth*(1+(r>0?o:-o)))}}this.points.push(n),this.points=this.points.slice(-3)}}}]),t}(),F=function(){function t(e){u(this,t),this.canvas=void 0,this._ee=void 0,this.pen=void 0;var n=new T(e);n.set("parent",this),this.canvas=n,this._ee=new _,this.pen=new W(n),this.init()}return h(t,[{key:"init",value:function(){this.pen.active=!0}},{key:"destroy",value:function(){this.canvas.destroy()}},{key:"clear",value:function(){this.pen.clear()}},{key:"undo",value:function(){this.pen.undo()}},{key:"redo",value:function(){this.pen.redo()}},{key:"save",value:function(){}},{key:"getContentBoundingBox",value:function(t){return this.pen.getContentBoundingBox(t)}},{key:"getMaskedImageData",value:function(t){return this.pen.getMaskedImageData(t)}},{key:"isEmpty",value:function(){return this.pen.isEmpty()}},{key:"on",value:function(t,e){this._ee.on(t,e)}},{key:"emit",value:function(t,e){this._ee.emit(t,e)}},{key:"off",value:function(t,e){this._ee.off(t,e)}}]),t}();e.Signature=F;var j=F;e.default=j},"6c3e":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.uniContext=e.toDataURL=e.createImage=void 0,e.useCurrentPage=function(){var t=getCurrentPages();return t[t.length-1]};var a=i(n("9b1b")),r=i(n("80b1")),o=i(n("efe5"));n("c223"),n("bf0f"),n("e966");uni.getSystemInfoSync().uniPlatform;e.uniContext=function(t,e){var n=uni.createCanvasContext(t,e);return n.uniDrawImage||(n.uniDrawImage=n.drawImage,n.drawImage=function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];n.uniDrawImage.apply(n,[t.src].concat(i))}),n.getImageData?(n._getImageData=n.getImageData,n.getImageData=function(t,e,i,a){return new Promise((function(r,o){n._getImageData({x:t,y:e,width:parseInt(i),height:parseInt(a),success:function(t){r(t)},fail:function(t){o(t)}})}))}):n.getImageData=function(n,i,a,r){return new Promise((function(o,s){e.proxy&&(e=e.proxy),uni.canvasGetImageData({canvasId:t,x:n,y:i,width:parseInt(a),height:parseInt(r),success:function(t){o(t)},fail:function(t){s(t)}},e)}))},n};var s=function(){function t(){(0,r.default)(this,t),this.currentSrc=null,this.naturalHeight=0,this.naturalWidth=0,this.width=0,this.height=0,this.tagName="IMG"}return(0,o.default)(t,[{key:"onerror",value:function(){}},{key:"onload",value:function(){}},{key:"src",get:function(){return this.currentSrc},set:function(t){var e=this;this.currentSrc=t,uni.getImageInfo({src:t,success:function(t){e.naturalWidth=e.width=t.width,e.naturalHeight=e.height=t.height,e.onload()},fail:function(){e.onerror()}})}}]),t}();e.createImage=function(){return new s};e.toDataURL=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise((function(i,r){var o=n.canvas,s=n.width,u=n.height,c=(n.destWidth,n.destHeight,n.x),h=void 0===c?0:c,l=n.y,f=void 0===l?0:l,d=n.preferToDataURL,v=uni.getSystemInfoSync(),p=v.pixelRatio,g=(0,a.default)((0,a.default)({},n),{},{canvasId:t,id:t,canvas:o,success:function(t){i(t.tempFilePath)},fail:function(t){r(t)}});if(o&&o.toDataURL&&d){uni.getSystemInfoSync().platform;if(h||f){var m=uni.createOffscreenCanvas({type:"2d"}),y=m.getContext("2d"),w=Math.floor(s*p),x=Math.floor(u*p);m.width=w,m.height=x;var b=o.createImage();b.onload=function(){y.drawImage(b,Math.floor(h*p),Math.floor(f*p),w,x,0,0,w,x);var t=m.toDataURL();i(t),g.success&&g.success({tempFilePath:t})},b.src=o.toDataURL()}else{var S=o.toDataURL();i(S),g.success&&g.success({tempFilePath:S})}}else o&&o.toTempFilePath?o.toTempFilePath(g):uni.canvasToTempFilePath(g,e)}))}},"85d6":function(t,e,n){"use strict";n.r(e);var i=n("2deb"),a=n("ab3c");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);var o=n("1ac2");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("2a29");var s=n("828b");a["default"].__module="sign";var u=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"3a035048",null,!1,i["a"],a["default"]);e["default"]=u.exports},"8ff3":function(t,e,n){"use strict";n.r(e);var i=n("bafc"),a=n("45cc");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("48a6");var o=n("828b"),s=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"0ec9ff0e",null,!1,i["a"],void 0);e["default"]=s.exports},9147:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("b198")),r=a.default;e.default=r},"94a2":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,".inner[data-v-0ec9ff0e]{width:90vw;margin:0 auto}.inner uni-view[data-v-0ec9ff0e]{text-indent:24px;line-height:4vh}",""]),t.exports=e},"95e1":function(t,e,n){var i=n("94a2");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("726128ca",i,!0,{sourceMap:!1,shadowMode:!1})},a54f:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.base64ToPath=function(t){var e=/data:image\/(\w+);base64,(.*)/.exec(t)||[],n=(0,a.default)(e,3);n[1],n[2];return new Promise((function(t,e){}))},e.canIUseCanvas2d=function(){return!1},e.compareVersion=r,e.getRect=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="boundingClientRect",i=e.context,a=e.type,r=void 0===a?n:a;return new Promise((function(e,a){var o=uni.createSelectorQuery().in(i).select(t),s=function(t){t?e(t):a()};r==n?o[r](s).exec():o[r]({node:!0,size:!0,rect:!0},s).exec()}))},e.isTransparent=function(t){if("transparent"===t)return!0;if(t.startsWith("rgba")){var e=t.match(/\d+(\.\d+)?/g);if(null!==e){var n=parseFloat(e[3]);if(0===n)return!0}}return!1},e.requestAnimationFrame=void 0,e.sleep=function(t){return new Promise((function(e){return setTimeout(e,t)}))},e.wrapEvent=void 0;var a=i(n("5de6"));function r(t,e){t=t.split("."),e=e.split(".");var n=Math.max(t.length,e.length);while(t.length<n)t.push("0");while(e.length<n)e.push("0");for(var i=0;i<n;i++){var a=parseInt(t[i],10),r=parseInt(e[i],10);if(a>r)return 1;if(a<r)return-1}return 0}n("aa9c"),n("e966"),n("5c47"),n("bf0f"),n("9db6"),n("2c10"),n("e838");e.wrapEvent=function(t){if(t)return t.preventDefault||(t.preventDefault=function(){}),t};e.requestAnimationFrame=function(t){setTimeout(t,30)}},ab3c:function(t,e,n){"use strict";n.r(e);var i=n("9147"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},b198:function(t,e){},bafc:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={lSignature:n("85d6").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{staticStyle:{"text-align":"center","font-weight":"bold","line-height":"6vh"}},[t._v("《签约中心服务协议》")]),n("v-uni-view",{staticClass:"inner"},[n("v-uni-view",[t._v("欢迎使用我们的签约服务。为了保护您的账号安全并提供更好的服务，本认证协议（以下简称“协议”）适用于您在使用我们的签约功能时的相关条款。")]),n("v-uni-view",{staticStyle:{"text-indent":"0","font-weight":"bold"}},[t._v("一、信息收集与使用")]),n("v-uni-view",[t._v("1.1 我们会收集您的姓名、身份证号码、联系方式等身份信息用于进行签约，以确保您在使用平台时的身份真实性。")]),n("v-uni-view",[t._v("1.2 我们承诺不会将您的个人信息用于与签约无关的其他用途，且不会将其出售给第三方。")]),n("v-uni-view",[t._v("1.3 为了顺利完成签约，您需要上传您的身份证正反面照片，确保照片清晰且无遮挡。")]),n("v-uni-view",{staticStyle:{"text-indent":"0","font-weight":"bold"}},[t._v("二、信息安全与隐私保护")]),n("v-uni-view",[t._v("2.1 我们将采取适当的技术和管理措施保护您的个人信息不受未经授权的访问、修改、披露或破坏。")]),n("v-uni-view",[t._v("2.2 您的认证信息将仅限用于身份验证，并会按照法律要求保留必要的时间。")]),n("v-uni-view",{staticStyle:{"text-indent":"0","font-weight":"bold"}},[t._v("三、用户的权利")]),n("v-uni-view",[t._v("3.1 您有权查看、修改和删除您提交的认证信息，但可能会影响您的认证状态。")]),n("v-uni-view",[t._v("3.2 若您发现认证信息错误或不准确，您可以随时请求我们更正。")]),n("v-uni-view",{staticStyle:{"text-indent":"0","font-weight":"bold"}},[t._v("四、认证流程")]),n("v-uni-view",[t._v("4.1 提交认证信息后，我们会进行审核。审核通过后，您将收到认证成功的通知。")]),n("v-uni-view",[t._v("4.2 若审核未通过，您可以根据系统提示修改信息后重新提交。")]),n("v-uni-view",{staticStyle:{"text-indent":"0","font-weight":"bold"}},[t._v("五、法律责任")]),n("v-uni-view",[t._v("5.1 本协议受中华人民共和国法律的管辖。如发生争议，双方应首先友好协商解决；协商不成时，任何一方可向有管辖权的法院提起诉讼。")]),n("v-uni-view",{staticStyle:{"text-indent":"0","font-weight":"bold"}},[t._v("六、协议变更")]),n("v-uni-view",[t._v("6.1 我们有权随时更新或修改本协议的内容，更新后的协议将通过平台公告或其他方式通知您。")]),n("v-uni-view",[t._v("6.2 请您定期查看协议内容，以确保您知悉协议的最新条款。")]),n("v-uni-view",{staticStyle:{"text-align":"center","font-size":"26rpx","line-height":"6vh"}},[t._v("本协议自您点击“同意并提交”之时起已生效。")])],1),null==t.imgurl?n("v-uni-view",[n("v-uni-text",{staticStyle:{"font-weight":"bold"}},[t._v("请签名：")]),n("v-uni-view",{staticStyle:{width:"750rpx",height:"750rpx","background-color":"#eee"}},[n("l-signature",{ref:"signatureRef",attrs:{penColor:t.penColor,penSize:t.penSize,openSmooth:t.openSmooth,type:!0}})],1),n("v-uni-view",[n("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("clear")}}},[t._v("清空")]),n("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("undo")}}},[t._v("撤消")]),n("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick("save")}}},[t._v("保存")])],1)],1):n("v-uni-view",[n("v-uni-text",{staticStyle:{"font-weight":"bold"}},[t._v("您的签名：")]),n("img",{staticStyle:{width:"700rpx",height:"300rpx",display:"block",margin:"0 auto"},attrs:{src:t.imgurl,alt:""}})],1)],1)},r=[]},bb48:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var i={styles:String,disableScroll:{type:Boolean,default:!0},type:{type:String,default:"2d"},penColor:{type:String,default:"black"},penSize:{type:Number,default:2},backgroundColor:String,backgroundImage:String,openSmooth:Boolean,minLineWidth:{type:Number,default:2},maxLineWidth:{type:Number,default:6},minSpeed:{type:Number,default:1.5},maxWidthDiffRate:{type:Number,default:20},maxHistoryLength:{type:Number,default:20},beforeDelay:{type:Number,default:0},landscape:{type:Boolean},boundingBox:{type:Boolean},disabled:{type:Boolean},preferToDataURL:Boolean};e.default=i}}]);