(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-project-detail-project-detail"],{"4a11":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("8f71"),i("bf0f");var a={data:function(){return{product:{},quantity:1}},onLoad:function(t){console.log(t),this.getdetial(t.id)},methods:{getdetial:function(t){var e=this;this.$api.request({url:this.$api.getrecommendlist}).then((function(i){console.log(i.rows,t);var a=i.rows.filter((function(e){return e.id==t}));console.log(a[0],"11111"),e.product=a[0],console.log(e.product)}))},createOrder:function(){if(this.quantity<=0)return uni.showToast({title:"请输入正确的数量",icon:"none"});var t={productId:this.product.id,productName:this.product.name,price:this.product.price,quantity:this.quantity,totalAmount:this.product.price*this.quantity,owner:this.product.owner};console.log("创建订单数据",t),uni.request({url:"https://api.example.com/orders/create",method:"POST",data:t,success:function(t){t.data.success?(uni.showToast({title:"订单创建成功",icon:"success"}),uni.navigateTo({url:"/pages/order-detail/order-detail?orderId="+t.data.orderId})):uni.showToast({title:t.data.message,icon:"none"})},fail:function(t){console.error("创建订单失败",t),uni.showToast({title:"创建订单失败",icon:"none"})}})}}};e.default=a},"66e1":function(t,e,i){"use strict";var a=i("c003"),o=i.n(a);o.a},"8bed":function(t,e,i){"use strict";i.r(e);var a=i("d2fe"),o=i("ee79");for(var n in o)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(n);i("66e1");var c=i("828b"),r=Object(c["a"])(o["default"],a["b"],a["c"],!1,null,"4c793df0",null,!1,a["a"],void 0);e["default"]=r.exports},c003:function(t,e,i){var a=i("c746");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("3859f287",a,!0,{sourceMap:!1,shadowMode:!1})},c746:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,".container[data-v-4c793df0]{display:flex;flex-direction:column;background-color:#f5f5f5}.header[data-v-4c793df0]{display:flex;justify-content:space-between;align-items:center;padding:%?10?% %?20?%;background-color:#3cb371;color:#fff}.title[data-v-4c793df0]{font-size:%?32?%}.refresh-btn[data-v-4c793df0]{background-color:#fff;color:#3cb371;padding:%?5?% %?10?%;border-radius:%?5?%}.image-swiper[data-v-4c793df0]{height:%?300?%;margin-bottom:%?20?%}.product-image[data-v-4c793df0]{width:100%;height:%?300?%}.info[data-v-4c793df0]{padding:%?20?%;background-color:#fff;margin-bottom:%?20?%}.info-row[data-v-4c793df0]{display:flex;justify-content:space-between;padding:%?20?%;margin-bottom:%?15?%}.label[data-v-4c793df0]{font-size:%?28?%;color:#555}.value[data-v-4c793df0]{font-size:%?28?%;color:#333}.quantity-input[data-v-4c793df0]{border:%?1?% solid #ddd;border-radius:%?5?%;padding:%?5?%;width:%?100?%;text-align:center}.description[data-v-4c793df0]{padding:%?20?%;background-color:#fff}.section-title[data-v-4c793df0]{font-size:%?28?%;color:#333;margin-bottom:%?10?%;text-align:center}.detail-image[data-v-4c793df0]{width:100%;margin-top:%?10?%}.action[data-v-4c793df0]{padding:%?20?%;background-color:#fff;margin-top:%?20?%;text-align:center}.order-btn[data-v-4c793df0]{background-color:#3cb371;color:#fff;padding:%?15?% %?30?%;border-radius:%?10?%;font-size:%?28?%}",""]),t.exports=e},d2fe:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"container"},[i("v-uni-image",{staticClass:"product-image",attrs:{src:t.product.sliderImage}}),i("v-uni-view",{staticClass:"info"},[i("v-uni-view",{staticClass:"info-row"},[i("v-uni-text",{staticClass:"label"},[t._v("商品名称：")]),i("v-uni-text",{staticClass:"value"},[t._v(t._s(t.product.storeName))])],1),i("v-uni-view",{staticClass:"info-row"},[i("v-uni-text",{staticClass:"label"},[t._v("价格：")]),i("v-uni-text",{staticClass:"value"},[t._v("¥ "+t._s(t.product.price))])],1),i("v-uni-view",{staticClass:"info-row"},[i("v-uni-text",{staticClass:"label"},[t._v("数量：")]),i("v-uni-text",{staticClass:"quantity-input"},[t._v(t._s(t.quantity))])],1)],1),i("v-uni-view",{staticClass:"description"},[i("v-uni-text",{staticClass:"section-title"},[t._v("-------- 商品详情 --------")]),i("v-uni-view",[t._v(t._s(t.product.storeInfo))]),i("v-uni-image",{staticClass:"detail-image",attrs:{src:t.product.image}})],1)],1)},o=[]},ee79:function(t,e,i){"use strict";i.r(e);var a=i("4a11"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a}}]);