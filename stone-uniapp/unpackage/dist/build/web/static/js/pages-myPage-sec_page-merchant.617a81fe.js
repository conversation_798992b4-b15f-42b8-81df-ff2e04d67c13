(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-myPage-sec_page-merchant"],{"05a2":function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement,t=this._self._c||n;return t("v-uni-view",[this._v("商家入驻")])},i=[]},"5f61":function(n,t){},c804:function(n,t,e){"use strict";e.r(t);var u=e("5f61"),i=e.n(u);for(var r in u)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(r);t["default"]=i.a},d670:function(n,t,e){"use strict";e.r(t);var u=e("05a2"),i=e("c804");for(var r in i)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(r);var c=e("828b"),a=Object(c["a"])(i["default"],u["b"],u["c"],!1,null,"71f2d5e2",null,!1,u["a"],void 0);t["default"]=a.exports}}]);