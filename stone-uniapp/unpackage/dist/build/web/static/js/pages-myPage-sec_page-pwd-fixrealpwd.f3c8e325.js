(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-myPage-sec_page-pwd-fixrealpwd"],{2639:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content"},[n("v-uni-input",{attrs:{type:"text",placeholder:"侵害输入手机号"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}}),n("v-uni-view",{staticClass:"item"},[n("v-uni-input",{attrs:{type:"text",placeholder:"验证码"},model:{value:e.form.phoneCode,callback:function(t){e.$set(e.form,"phoneCode",t)},expression:"form.phoneCode"}}),n("v-uni-button",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendCode.apply(void 0,arguments)}}},[e._v(e._s(e.isCooldown?e.cooldown+"s 后重新发送":"获取验证码"))])],1),n("v-uni-view",{staticClass:"speace"}),n("v-uni-input",{attrs:{type:"text",placeholder:"请输入新的交易密码"},model:{value:e.form.transactionPassword,callback:function(t){e.$set(e.form,"transactionPassword",t)},expression:"form.transactionPassword"}}),n("v-uni-button",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirmFix.apply(void 0,arguments)}}},[e._v("确认修改")])],1)},i=[]},"487b":function(e,t,n){var o=n("c86c");t=o(!1),t.push([e.i,".content[data-v-36e23cd0]{display:flex;flex-direction:column}.speace[data-v-36e23cd0]{height:4vh}uni-input[data-v-36e23cd0]{width:80vw;height:8vh;padding:0 2vh;margin:1vh auto;border-radius:4vh;background-color:#eee}.item[data-v-36e23cd0]{width:80vw;height:8vh;margin:0 auto;padding:0 2vh;display:flex;flex-direction:row;justify-content:space-between}.item uni-input[data-v-36e23cd0]{width:60%;margin:0}.item uni-button[data-v-36e23cd0]{width:40%;height:100%;line-height:8vh;background-color:green;border-radius:4vh;font-size:%?28?%;color:#fff}",""]),e.exports=t},8183:function(e,t,n){"use strict";n.r(t);var o=n("f3a7"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},"96a4":function(e,t,n){"use strict";n.r(t);var o=n("2639"),i=n("8183");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("977d");var r=n("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,"36e23cd0",null,!1,o["a"],void 0);t["default"]=s.exports},"977d":function(e,t,n){"use strict";var o=n("c057"),i=n.n(o);i.a},c057:function(e,t,n){var o=n("487b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=n("967d").default;i("371e82ad",o,!0,{sourceMap:!1,shadowMode:!1})},f3a7:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={data:function(){return{cooldown:0,isCooldown:!1,form:{userId:"",phone:"",phoneCode:"",transactionPassword:""}}},methods:{sendCode:function(){var e=this;this.$api.request({url:this.$api.getdecode+"?phone="+this.form.phone}).then((function(e){console.log(e),uni.showToast({title:"验证码已发送",icon:"success"})})),this.cooldown=60,this.isCooldown=!0;var t=setInterval((function(){e.cooldown-=1,e.cooldown<=0&&(clearInterval(t),e.isCooldown=!1)}),1e3)},confirmFix:function(){var e=uni.getStorageSync("userInfo");this.form.userId=e.id,this.$api.request({url:this.$api.fixpaypwd,method:"POST",data:this.form}).then((function(e){console.log(e),uni.showToast({title:"修改成功"}),setTimeout((function(){uni.navigateTo({url:"/pages/login/login"})}),2e3)}))}}};t.default=o}}]);