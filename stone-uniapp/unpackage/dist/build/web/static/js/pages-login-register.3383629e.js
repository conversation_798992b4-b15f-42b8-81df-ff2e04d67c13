(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-register"],{"062a":function(r,t,o){"use strict";o.r(t);var s=o("2224"),e=o("4e14");for(var a in e)["default"].indexOf(a)<0&&function(r){o.d(t,r,(function(){return e[r]}))}(a);o("dd64");var n=o("828b"),i=Object(n["a"])(e["default"],s["b"],s["c"],!1,null,"1f8e1812",null,!1,s["a"],void 0);t["default"]=i.exports},"07f7":function(r,t,o){var s=o("c86c");t=s(!1),t.push([r.i,".container[data-v-1f8e1812]{height:86vh;padding:%?20?%;background-color:#f5f5f5}.header[data-v-1f8e1812]{text-align:center;padding:%?20?%;font-size:%?36?%;color:#333;margin-bottom:%?20?%}.form[data-v-1f8e1812]{padding:%?20?%;border-radius:%?10?%;box-shadow:0 %?2?% %?5?% rgba(0,0,0,.1)}.input[data-v-1f8e1812]{width:100%;padding:%?10?%;margin-bottom:%?10?%;border:%?1?% solid #ddd;border-radius:%?5?%;font-size:%?28?%}.error[data-v-1f8e1812]{color:#f44336;font-size:%?24?%;margin-bottom:%?10?%}.verification[data-v-1f8e1812]{margin:10px;width:50%}.send-code-btn[data-v-1f8e1812]{padding:%?10?% %?20?%;background-color:#3cb371;color:#fff;border-radius:10px;font-size:%?28?%}.agreement[data-v-1f8e1812]{display:flex;align-items:center;margin:10px 0;font-size:%?24?%}.link[data-v-1f8e1812]{color:#3cb371;margin-left:%?5?%}.submit-btn[data-v-1f8e1812]{width:100%;background-color:#3cb371;color:#fff;padding:%?15?% 0;border-radius:10px;font-size:%?32?%;text-align:center}.app-download[data-v-1f8e1812]{width:100%;text-align:center;color:#f57c00;margin-top:%?20?%;font-size:%?28?%;position:fixed;left:0;bottom:15%}.form-group[data-v-1f8e1812]{margin:%?10?%}.form-group uni-input[data-v-1f8e1812]{width:90%;border-radius:15px;padding:1vh 2vw}",""]),r.exports=t},2224:function(r,t,o){"use strict";o.d(t,"b",(function(){return s})),o.d(t,"c",(function(){return e})),o.d(t,"a",(function(){}));var s=function(){var r=this,t=r.$createElement,o=r._self._c||t;return o("v-uni-view",{staticClass:"container"},[o("v-uni-view",{staticClass:"form"},[o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",[r._v("真实姓名："),r.errors.name?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.name))]):r._e()],1),o("v-uni-input",{staticClass:"input",attrs:{placeholder:"真实姓名，注册后不能修改"},model:{value:r.form.name,callback:function(t){r.$set(r.form,"name",t)},expression:"form.name"}})],1),o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",[r._v("手机号："),r.errors.phone?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.phone))]):r._e()],1),o("v-uni-input",{staticClass:"input",attrs:{placeholder:"真实手机号码",maxlength:"11",type:"number"},model:{value:r.form.phone,callback:function(t){r.$set(r.form,"phone",t)},expression:"form.phone"}})],1),o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",[r._v("登陆密码："),r.errors.password?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.password))]):r._e()],1),o("v-uni-input",{staticClass:"input",attrs:{placeholder:"登录密码",type:"password"},model:{value:r.form.password,callback:function(t){r.$set(r.form,"password",t)},expression:"form.password"}})],1),o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",[r._v("确认密码："),r.errors.password_2?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.password_2))]):r._e()],1),o("v-uni-input",{staticClass:"input",attrs:{placeholder:"确认密码",type:"password"},model:{value:r.form.password_2,callback:function(t){r.$set(r.form,"password_2",t)},expression:"form.password_2"}})],1),o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",[r._v("交易密码："),r.errors.transactionPassword?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.transactionPassword))]):r._e()],1),o("v-uni-input",{staticClass:"input",attrs:{placeholder:"交易密码",type:"password"},model:{value:r.form.transactionPassword,callback:function(t){r.$set(r.form,"transactionPassword",t)},expression:"form.transactionPassword"}})],1),o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",[r._v("确认交易密码："),r.errors.transactionPassword_2?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.transactionPassword_2))]):r._e()],1),o("v-uni-input",{staticClass:"input",attrs:{placeholder:"确认交易密码",type:"password"},model:{value:r.form.transactionPassword_2,callback:function(t){r.$set(r.form,"transactionPassword_2",t)},expression:"form.transactionPassword_2"}})],1),o("v-uni-view",{staticClass:"form-group"},[o("v-uni-view",{staticClass:"agreement"},[o("v-uni-checkbox",{attrs:{checked:r.form.agree},on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.checked.apply(void 0,arguments)}}}),o("v-uni-text",[r._v("已阅读并同意")]),o("v-uni-text",{staticClass:"link",on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.viewAgreement("terms")}}},[r._v("《会员协议》")]),o("v-uni-text",[r._v("和")]),o("v-uni-text",{staticClass:"link",on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.viewAgreement("privacy")}}},[r._v("《隐私政策》")])],1),r.errors.agree?o("v-uni-text",{staticClass:"error"},[r._v(r._s(r.errors.agree))]):r._e()],1),o("v-uni-button",{staticClass:"submit-btn",on:{click:function(t){arguments[0]=t=r.$handleEvent(t),r.register.apply(void 0,arguments)}}},[r._v("确定注册")])],1)],1)},e=[]},"3a8d":function(r,t,o){var s=o("07f7");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[r.i,s,""]]),s.locals&&(r.exports=s.locals);var e=o("967d").default;e("372fd141",s,!0,{sourceMap:!1,shadowMode:!1})},"4e14":function(r,t,o){"use strict";o.r(t);var s=o("74c0"),e=o.n(s);for(var a in s)["default"].indexOf(a)<0&&function(r){o.d(t,r,(function(){return s[r]}))}(a);t["default"]=e.a},"74c0":function(r,t,o){"use strict";o("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("5c47"),o("0506"),o("dc8a");var s={data:function(){return{form:{phone:"",name:"",password:"",password_2:"",transactionPassword:"",transactionPassword_2:"",agree:!1},errors:{},cooldown:0,isCooldown:!1}},onShow:function(){this.form={phone:"",name:"",password:"",transactionPassword:"",agree:!1}},methods:{validateForm:function(){return this.errors={},this.form.phone&&/^1[3-9]\d{9}$/.test(this.form.phone)||(this.errors.phone="请输入正确的手机号"),this.form.code&&6===this.form.code.length||(this.errors.code="请输入6位验证码"),this.form.name||(this.errors.name="请输入真实姓名"),(!this.form.password||this.form.password.length<6)&&(this.errors.password="密码至少6位"),(!this.form.password_2||this.form.password_2.length<6||this.form.password_2!=this.form.password)&&(this.errors.password_2="请确认与密码一致"),(!this.form.transactionPassword||this.form.transactionPassword.length<6)&&(this.errors.transactionPassword="交易密码至少6位"),(!this.form.transactionPassword_2||this.form.transactionPassword_2.length<6||this.form.transactionPassword_2!=this.form.transactionPassword)&&(this.errors.transactionPassword_2="请确认与交易密码一致"),this.form.agree||(this.errors.agree="请阅读并同意相关协议"),0===Object.keys(this.errors).length},sendCode:function(){var r=this;if(!this.form.phone||!/^1[3-9]\d{9}$/.test(this.form.phone))return this.errors.phone="请输入正确的手机号",void uni.showToast({title:"请输入正确的手机号",icon:"none"});this.errors.phone="",console.log("发送验证码到:",this.form.phone),this.cooldown=60,this.isCooldown=!0;var t=setInterval((function(){r.cooldown-=1,r.cooldown<=0&&(clearInterval(t),r.isCooldown=!1)}),1e3)},register:function(){var r=this;console.log("提交注册信息:",this.form),this.$api.request({url:this.$api.register,method:"POST",data:{password:this.form.password,phone:this.form.phone,realName:this.form.name,transactionPassword:this.form.transactionPassword}}).then((function(t){if(console.log(t,"发起请求"),200==t.code){uni.showToast({title:"注册成功",icon:"success"});var o={phone:r.form.phone,password:r.form.password,loginType:"1"};r.apiEvent(o)}else uni.showToast({title:t.data.msg,icon:"error"})}))},viewAgreement:function(r){var t="";"terms"===r&&(t="/pages/terms/terms"),"privacy"===r&&(t="/pages/privacy/privacy"),uni.navigateTo({url:t})},checked:function(r){this.form.agree=!this.form.agree},apiEvent:function(r){this.$api.request({url:this.$api.tologinbycp,method:"POST",data:r}).then((function(r){console.log(r,"返回");var t=r.appUser,o={id:t.id,headImg:t.headImg,nickName:t.nickName,phone:t.phone,realName:t.realName};uni.setStorageSync("user_count_key",r.token),uni.setStorageSync("userInfo",o),uni.showToast({title:"注册成功"}),setTimeout((function(){uni.reLaunch({url:"/pages/myPage/sec_page/getpay"})}),1200)}))},touchEvent:function(){console.log("1111")}}};t.default=s},dd64:function(r,t,o){"use strict";var s=o("3a8d"),e=o.n(s);e.a}}]);