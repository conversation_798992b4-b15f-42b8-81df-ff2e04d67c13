(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-myPage-sec_page-sharecode"],{"180f":function(t,e,o){"use strict";o.r(e);var r=o("71c5"),n=o.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},1851:function(t,e,o){"use strict";var r=o("8bdb"),n=o("84d6"),i=o("1cb5");r({target:"Array",proto:!0},{fill:n}),i("fill")},"3f02":function(t,e,o){var r=o("c86c");e=r(!1),e.push([t.i,".code[data-v-8086cc36]{width:100%;background:url(http://**************:82/background.png) no-repeat;background-size:100% 100%;position:relative;overflow:hidden;display:flex}img[data-v-8086cc36]{width:100%;height:100%}.qrcode[data-v-8086cc36]{position:absolute;background-color:#fff;left:50%;margin-left:-75px;bottom:8vh;z-index:1000}uni-text[data-v-8086cc36]{position:fixed;left:0;bottom:%?40?%;width:100%;text-align:center;color:#fff;font-size:%?18?%}",""]),t.exports=e},4085:function(t,e,o){"use strict";var r=o("8bdb"),n=o("85c1");r({global:!0,forced:n.globalThis!==n},{globalThis:n})},4151:function(t,e,o){(function(r){var n,i,a=o("dd7e").default,u=o("46c5").default,s=o("5014").default,d=o("bdbb").default;o("4085"),o("aa9c"),o("7a76"),o("c9b5"),o("a03a"),o("64aa"),o("bf0f"),o("2797"),o("dc8a"),o("ab80"),o("4626"),o("5ac7"),o("1851"),function(r,a){"object"==d(e)&&"undefined"!=typeof t?t.exports=a():(n=a,i="function"===typeof n?n.call(e,o,e,t):n,void 0===i||(t.exports=i))}("undefined"!==typeof window&&window,(function(){function t(t){this.mode=o.MODE_8BIT_BYTE,this.data=t}function e(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=new Array}t.prototype={getLength:function(t){return this.data.length},write:function(t){for(var e=0;e<this.data.length;e++)t.put(this.data.charCodeAt(e),8)}},e.prototype={addData:function(e){var o=new t(e);this.dataList.push(o),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){if(this.typeNumber<1){var t=1;for(t=1;t<40;t++){for(var e=l.getRSBlocks(t,this.errorCorrectLevel),o=new g,r=0,i=0;i<e.length;i++)r+=e[i].dataCount;for(i=0;i<this.dataList.length;i++){var a=this.dataList[i];o.put(a.mode,4),o.put(a.getLength(),n.getLengthInBits(a.mode,t)),a.write(o)}if(o.getLengthInBits()<=8*r)break}this.typeNumber=t}this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,o){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++)this.modules[r][n]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,o),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=e.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,o)},setupPositionProbePattern:function(t,e){for(var o=-1;o<=7;o++)if(!(t+o<=-1||this.moduleCount<=t+o))for(var r=-1;r<=7;r++)e+r<=-1||this.moduleCount<=e+r||(this.modules[t+o][e+r]=0<=o&&o<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==o||6==o)||2<=o&&o<=4&&2<=r&&r<=4)},getBestMaskPattern:function(){for(var t=0,e=0,o=0;o<8;o++){this.makeImpl(!0,o);var r=n.getLostPoint(this);(0==o||t>r)&&(t=r,e=o)}return e},createMovieClip:function(t,e,o){var r=t.createEmptyMovieClip(e,o);this.make();for(var n=0;n<this.modules.length;n++)for(var i=1*n,a=0;a<this.modules[n].length;a++){var u=1*a;this.modules[n][a]&&(r.beginFill(0,100),r.moveTo(u,i),r.lineTo(u+1,i),r.lineTo(u+1,i+1),r.lineTo(u,i+1),r.endFill())}return r},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=n.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var o=0;o<t.length;o++){var r=t[e],i=t[o];if(null==this.modules[r][i])for(var a=-2;a<=2;a++)for(var u=-2;u<=2;u++)this.modules[r+a][i+u]=-2==a||2==a||-2==u||2==u||0==a&&0==u}},setupTypeNumber:function(t){for(var e=n.getBCHTypeNumber(this.typeNumber),o=0;o<18;o++){var r=!t&&1==(e>>o&1);this.modules[Math.floor(o/3)][o%3+this.moduleCount-8-3]=r}for(o=0;o<18;o++)r=!t&&1==(e>>o&1),this.modules[o%3+this.moduleCount-8-3][Math.floor(o/3)]=r},setupTypeInfo:function(t,e){for(var o=this.errorCorrectLevel<<3|e,r=n.getBCHTypeInfo(o),i=0;i<15;i++){var a=!t&&1==(r>>i&1);i<6?this.modules[i][8]=a:i<8?this.modules[i+1][8]=a:this.modules[this.moduleCount-15+i][8]=a}for(i=0;i<15;i++)a=!t&&1==(r>>i&1),i<8?this.modules[8][this.moduleCount-i-1]=a:i<9?this.modules[8][15-i-1+1]=a:this.modules[8][15-i-1]=a;this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var o=-1,r=this.moduleCount-1,i=7,a=0,u=this.moduleCount-1;u>0;u-=2)for(6==u&&u--;;){for(var s=0;s<2;s++)if(null==this.modules[r][u-s]){var d=!1;a<t.length&&(d=1==(t[a]>>>i&1)),n.getMask(e,r,u-s)&&(d=!d),this.modules[r][u-s]=d,-1==--i&&(a++,i=7)}if((r+=o)<0||this.moduleCount<=r){r-=o,o=-o;break}}}},e.PAD0=236,e.PAD1=17,e.createData=function(t,o,r){for(var i=l.getRSBlocks(t,o),a=new g,u=0;u<r.length;u++){var s=r[u];a.put(s.mode,4),a.put(s.getLength(),n.getLengthInBits(s.mode,t)),s.write(a)}var d=0;for(u=0;u<i.length;u++)d+=i[u].dataCount;if(a.getLengthInBits()>8*d)throw new Error("code length overflow. ("+a.getLengthInBits()+">"+8*d+")");for(a.getLengthInBits()+4<=8*d&&a.put(0,4);a.getLengthInBits()%8!=0;)a.putBit(!1);for(;!(a.getLengthInBits()>=8*d||(a.put(e.PAD0,8),a.getLengthInBits()>=8*d));)a.put(e.PAD1,8);return e.createBytes(a,i)},e.createBytes=function(t,e){for(var o=0,r=0,i=0,a=new Array(e.length),u=new Array(e.length),s=0;s<e.length;s++){var d=e[s].dataCount,l=e[s].totalCount-d;r=Math.max(r,d),i=Math.max(i,l),a[s]=new Array(d);for(var g=0;g<a[s].length;g++)a[s][g]=255&t.buffer[g+o];o+=d;var h=n.getErrorCorrectPolynomial(l),f=new c(a[s],h.getLength()-1).mod(h);for(u[s]=new Array(h.getLength()-1),g=0;g<u[s].length;g++){var m=g+f.getLength()-u[s].length;u[s][g]=m>=0?f.get(m):0}}var p=0;for(g=0;g<e.length;g++)p+=e[g].totalCount;var v=new Array(p),b=0;for(g=0;g<r;g++)for(s=0;s<e.length;s++)g<a[s].length&&(v[b++]=a[s][g]);for(g=0;g<i;g++)for(s=0;s<e.length;s++)g<u[s].length&&(v[b++]=u[s][g]);return v};for(var o={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},r={L:1,M:0,Q:3,H:2},n={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){for(var e=t<<10;n.getBCHDigit(e)-n.getBCHDigit(n.G15)>=0;)e^=n.G15<<n.getBCHDigit(e)-n.getBCHDigit(n.G15);return(t<<10|e)^n.G15_MASK},getBCHTypeNumber:function(t){for(var e=t<<12;n.getBCHDigit(e)-n.getBCHDigit(n.G18)>=0;)e^=n.G18<<n.getBCHDigit(e)-n.getBCHDigit(n.G18);return t<<12|e},getBCHDigit:function(t){for(var e=0;0!=t;)e++,t>>>=1;return e},getPatternPosition:function(t){return n.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,o){switch(t){case 0:return(e+o)%2==0;case 1:return e%2==0;case 2:return o%3==0;case 3:return(e+o)%3==0;case 4:return(Math.floor(e/2)+Math.floor(o/3))%2==0;case 5:return e*o%2+e*o%3==0;case 6:return(e*o%2+e*o%3)%2==0;case 7:return(e*o%3+(e+o)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new c([1],0),o=0;o<t;o++)e=e.multiply(new c([1,i.gexp(o)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case o.MODE_NUMBER:return 10;case o.MODE_ALPHA_NUM:return 9;case o.MODE_8BIT_BYTE:case o.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case o.MODE_NUMBER:return 12;case o.MODE_ALPHA_NUM:return 11;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case o.MODE_NUMBER:return 14;case o.MODE_ALPHA_NUM:return 13;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),o=0,r=0;r<e;r++)for(var n=0;n<e;n++){for(var i=0,a=t.isDark(r,n),u=-1;u<=1;u++)if(!(r+u<0||e<=r+u))for(var s=-1;s<=1;s++)n+s<0||e<=n+s||0==u&&0==s||a==t.isDark(r+u,n+s)&&i++;i>5&&(o+=3+i-5)}for(r=0;r<e-1;r++)for(n=0;n<e-1;n++){var d=0;t.isDark(r,n)&&d++,t.isDark(r+1,n)&&d++,t.isDark(r,n+1)&&d++,t.isDark(r+1,n+1)&&d++,0!=d&&4!=d||(o+=3)}for(r=0;r<e;r++)for(n=0;n<e-6;n++)t.isDark(r,n)&&!t.isDark(r,n+1)&&t.isDark(r,n+2)&&t.isDark(r,n+3)&&t.isDark(r,n+4)&&!t.isDark(r,n+5)&&t.isDark(r,n+6)&&(o+=40);for(n=0;n<e;n++)for(r=0;r<e-6;r++)t.isDark(r,n)&&!t.isDark(r+1,n)&&t.isDark(r+2,n)&&t.isDark(r+3,n)&&t.isDark(r+4,n)&&!t.isDark(r+5,n)&&t.isDark(r+6,n)&&(o+=40);var c=0;for(n=0;n<e;n++)for(r=0;r<e;r++)t.isDark(r,n)&&c++;return o+Math.abs(100*c/e/e-50)/5*10}},i={glog:function(t){if(t<1)throw new Error("glog("+t+")");return i.LOG_TABLE[t]},gexp:function(t){for(;t<0;)t+=255;for(;t>=256;)t-=255;return i.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},d=0;d<8;d++)i.EXP_TABLE[d]=1<<d;for(d=8;d<256;d++)i.EXP_TABLE[d]=i.EXP_TABLE[d-4]^i.EXP_TABLE[d-5]^i.EXP_TABLE[d-6]^i.EXP_TABLE[d-8];for(d=0;d<255;d++)i.LOG_TABLE[i.EXP_TABLE[d]]=d;function c(t,e){if(null==t.length)throw new Error(t.length+"/"+e);for(var o=0;o<t.length&&0==t[o];)o++;this.num=new Array(t.length-o+e);for(var r=0;r<t.length-o;r++)this.num[r]=t[r+o]}function l(t,e){this.totalCount=t,this.dataCount=e}function g(){this.buffer=new Array,this.length=0}function h(t){return t.setFillStyle=t.setFillStyle||function(e){t.fillStyle=e},t.setFontSize=t.setFontSize||function(e){t.font="".concat(e,"px")},t.setTextAlign=t.setTextAlign||function(e){t.textAlign=e},t.setTextBaseline=t.setTextBaseline||function(e){t.textBaseline=e},t.setGlobalAlpha=t.setGlobalAlpha||function(e){t.globalAlpha=e},t.setStrokeStyle=t.setStrokeStyle||function(e){t.strokeStyle=e},t.setShadow=t.setShadow||function(e,o,r,n){t.shadowOffsetX=e,t.shadowOffsetY=o,t.shadowBlur=r,t.shadowColor=n},t.draw=t.draw||function(t,e){e&&e()},t}function f(t,e){var o=this,r=this.data="";this.dataEncode=!0;var n=this.size=200;this.useDynamicSize=!1,this.dynamicSize=n;var i=this.typeNumber=-1;this.errorCorrectLevel=f.errorCorrectLevel.H;var a=this.margin=0;this.areaColor="#FFFFFF",this.backgroundColor="rgba(255,255,255,0)",this.backgroundImageSrc=void 0;var u=this.backgroundImageWidth=void 0,s=this.backgroundImageHeight=void 0,d=this.backgroundImageX=void 0,c=this.backgroundImageY=void 0;this.backgroundImageAlpha=1,this.backgroundImageBorderRadius=0;var l=this.backgroundPadding=0;this.foregroundColor="#000000",this.foregroundImageSrc=void 0;var g=this.foregroundImageWidth=void 0,m=this.foregroundImageHeight=void 0,p=this.foregroundImageX=void 0,v=this.foregroundImageY=void 0,b=this.foregroundImagePadding=0;this.foregroundImageBackgroundColor="#FFFFFF";var y=this.foregroundImageBorderRadius=0,C=this.foregroundImageShadowOffsetX=0,w=this.foregroundImageShadowOffsetY=0,k=this.foregroundImageShadowBlur=0;this.foregroundImageShadowColor="#808080";var I=this.foregroundPadding=0,B=this.positionProbeBackgroundColor=void 0,S=this.positionProbeForegroundColor=void 0,x=this.separatorColor=void 0,P=this.positionAdjustBackgroundColor=void 0,L=this.positionAdjustForegroundColor=void 0,E=this.timingBackgroundColor=void 0,_=this.timingForegroundColor=void 0,T=this.typeNumberBackgroundColor=void 0,D=this.typeNumberForegroundColor=void 0,O=this.darkBlockColor=void 0;this.base=void 0,this.modules=[],this.moduleCount=0,this.drawModules=[];var A=this.canvasContext=void 0;this.loadImage,this.drawReserve=!1,this.isMaked=!1,Object.defineProperties(this,{data:{get:function(){if(""===r||void 0===r)throw console.error("[uQRCode]: data must be set!"),new f.Error("data must be set!");return r},set:function(t){r=String(t)}},size:{get:function(){return n},set:function(t){n=Number(t)}},typeNumber:{get:function(){return i},set:function(t){i=Number(t)}},margin:{get:function(){return a},set:function(t){a=Number(t)}},backgroundImageWidth:{get:function(){return void 0===u?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*u:u},set:function(t){u=Number(t)}},backgroundImageHeight:{get:function(){return void 0===s?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*s:s},set:function(t){s=Number(t)}},backgroundImageX:{get:function(){return void 0===d?0:this.useDynamicSize?this.dynamicSize/this.size*d:d},set:function(t){d=Number(t)}},backgroundImageY:{get:function(){return void 0===c?0:this.useDynamicSize?this.dynamicSize/this.size*c:c},set:function(t){c=Number(t)}},backgroundPadding:{get:function(){return l},set:function(t){l=t>1?1:t<0?0:t}},foregroundImageWidth:{get:function(){return void 0===g?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*g:g},set:function(t){g=Number(t)}},foregroundImageHeight:{get:function(){return void 0===m?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*m:m},set:function(t){m=Number(t)}},foregroundImageX:{get:function(){return void 0===p?this.dynamicSize/2-this.foregroundImageWidth/2:this.useDynamicSize?this.dynamicSize/this.size*p:p},set:function(t){p=Number(t)}},foregroundImageY:{get:function(){return void 0===v?this.dynamicSize/2-this.foregroundImageHeight/2:this.useDynamicSize?this.dynamicSize/this.size*v:v},set:function(t){v=Number(t)}},foregroundImagePadding:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*b:b},set:function(t){b=Number(t)}},foregroundImageBorderRadius:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*y:y},set:function(t){y=Number(t)}},foregroundImageShadowOffsetX:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*C:C},set:function(t){C=Number(t)}},foregroundImageShadowOffsetY:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*w:w},set:function(t){w=Number(t)}},foregroundImageShadowBlur:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*k:k},set:function(t){k=Number(t)}},foregroundPadding:{get:function(){return I},set:function(t){I=t>1?1:t<0?0:t}},positionProbeBackgroundColor:{get:function(){return B||this.backgroundColor},set:function(t){B=t}},positionProbeForegroundColor:{get:function(){return S||this.foregroundColor},set:function(t){S=t}},separatorColor:{get:function(){return x||this.backgroundColor},set:function(t){x=t}},positionAdjustBackgroundColor:{get:function(){return P||this.backgroundColor},set:function(t){P=t}},positionAdjustForegroundColor:{get:function(){return L||this.foregroundColor},set:function(t){L=t}},timingBackgroundColor:{get:function(){return E||this.backgroundColor},set:function(t){E=t}},timingForegroundColor:{get:function(){return _||this.foregroundColor},set:function(t){_=t}},typeNumberBackgroundColor:{get:function(){return T||this.backgroundColor},set:function(t){T=t}},typeNumberForegroundColor:{get:function(){return D||this.foregroundColor},set:function(t){D=t}},darkBlockColor:{get:function(){return O||this.foregroundColor},set:function(t){O=t}},canvasContext:{get:function(){if(void 0===A)throw console.error("[uQRCode]: use drawCanvas, you need to set the canvasContext!"),new f.Error("use drawCanvas, you need to set the canvasContext!");return A},set:function(t){A=h(t)}}}),f.plugins.forEach((function(t){return t(f,o,!1)})),t&&this.setOptions(t),e&&(this.canvasContext=h(e))}return c.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),o=0;o<this.getLength();o++)for(var r=0;r<t.getLength();r++)e[o+r]^=i.gexp(i.glog(this.get(o))+i.glog(t.get(r)));return new c(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=i.glog(this.get(0))-i.glog(t.get(0)),o=new Array(this.getLength()),r=0;r<this.getLength();r++)o[r]=this.get(r);for(r=0;r<t.getLength();r++)o[r]^=i.gexp(i.glog(t.get(r))+e);return new c(o,0).mod(t)}},l.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],l.getRSBlocks=function(t,e){var o=l.getRsBlockTable(t,e);if(null==o)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var r=o.length/3,n=new Array,i=0;i<r;i++)for(var a=o[3*i+0],u=o[3*i+1],s=o[3*i+2],d=0;d<a;d++)n.push(new l(u,s));return n},l.getRsBlockTable=function(t,e){switch(e){case r.L:return l.RS_BLOCK_TABLE[4*(t-1)+0];case r.M:return l.RS_BLOCK_TABLE[4*(t-1)+1];case r.Q:return l.RS_BLOCK_TABLE[4*(t-1)+2];case r.H:return l.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},g.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var o=0;o<e;o++)this.putBit(1==(t>>>e-o-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},e.errorCorrectLevel=r,f.errorCorrectLevel=e.errorCorrectLevel,f.Error=function(t){this.errMsg="[uQRCode]: "+t},f.plugins=[],f.use=function(t){"function"==typeof t&&f.plugins.push(t)},f.prototype.loadImage=function(t){return Promise.resolve(t)},f.prototype.setOptions=function(t){var e,o,r,n,i,a,u,d,c,l,g,h,f,m,p,v,b,y,C,w,k,I,B,S,x,P,L,E,_,T,D,O,A,M,N,z,R,F,j,H,X,Y,G,W,K,Q,U,J,q,$,V,Z,tt,et,ot,rt,nt=this;t&&(Object.keys(t).forEach((function(e){nt[e]=t[e]})),function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var n in t=r?e:s({},e),o){var i=o[n];null!=i&&(i.constructor==Object?t[n]=this.deepReplace(t[n],i):i.constructor!=String||i?t[n]=i:t[n]=t[n])}}(this,{data:t.data||t.text,dataEncode:t.dataEncode,size:t.size,useDynamicSize:t.useDynamicSize,typeNumber:t.typeNumber,errorCorrectLevel:t.errorCorrectLevel,margin:t.margin,areaColor:t.areaColor,backgroundColor:t.backgroundColor||(null===(e=t.background)||void 0===e?void 0:e.color),backgroundImageSrc:t.backgroundImageSrc||(null===(o=t.background)||void 0===o||null===(r=o.image)||void 0===r?void 0:r.src),backgroundImageWidth:t.backgroundImageWidth||(null===(n=t.background)||void 0===n||null===(i=n.image)||void 0===i?void 0:i.width),backgroundImageHeight:t.backgroundImageHeight||(null===(a=t.background)||void 0===a||null===(u=a.image)||void 0===u?void 0:u.height),backgroundImageX:t.backgroundImageX||(null===(d=t.background)||void 0===d||null===(c=d.image)||void 0===c?void 0:c.x),backgroundImageY:t.backgroundImageY||(null===(l=t.background)||void 0===l||null===(g=l.image)||void 0===g?void 0:g.y),backgroundImageAlpha:t.backgroundImageAlpha||(null===(h=t.background)||void 0===h||null===(f=h.image)||void 0===f?void 0:f.alpha),backgroundImageBorderRadius:t.backgroundImageBorderRadius||(null===(m=t.background)||void 0===m||null===(p=m.image)||void 0===p?void 0:p.borderRadius),backgroundPadding:t.backgroundPadding,foregroundColor:t.foregroundColor||(null===(v=t.foreground)||void 0===v?void 0:v.color),foregroundImageSrc:t.foregroundImageSrc||(null===(b=t.foreground)||void 0===b||null===(y=b.image)||void 0===y?void 0:y.src),foregroundImageWidth:t.foregroundImageWidth||(null===(C=t.foreground)||void 0===C||null===(w=C.image)||void 0===w?void 0:w.width),foregroundImageHeight:t.foregroundImageHeight||(null===(k=t.foreground)||void 0===k||null===(I=k.image)||void 0===I?void 0:I.height),foregroundImageX:t.foregroundImageX||(null===(B=t.foreground)||void 0===B||null===(S=B.image)||void 0===S?void 0:S.x),foregroundImageY:t.foregroundImageY||(null===(x=t.foreground)||void 0===x||null===(P=x.image)||void 0===P?void 0:P.y),foregroundImagePadding:t.foregroundImagePadding||(null===(L=t.foreground)||void 0===L||null===(E=L.image)||void 0===E?void 0:E.padding),foregroundImageBackgroundColor:t.foregroundImageBackgroundColor||(null===(_=t.foreground)||void 0===_||null===(T=_.image)||void 0===T?void 0:T.backgroundColor),foregroundImageBorderRadius:t.foregroundImageBorderRadius||(null===(D=t.foreground)||void 0===D||null===(O=D.image)||void 0===O?void 0:O.borderRadius),foregroundImageShadowOffsetX:t.foregroundImageShadowOffsetX||(null===(A=t.foreground)||void 0===A||null===(M=A.image)||void 0===M?void 0:M.shadowOffsetX),foregroundImageShadowOffsetY:t.foregroundImageShadowOffsetY||(null===(N=t.foreground)||void 0===N||null===(z=N.image)||void 0===z?void 0:z.shadowOffsetY),foregroundImageShadowBlur:t.foregroundImageShadowBlur||(null===(R=t.foreground)||void 0===R||null===(F=R.image)||void 0===F?void 0:F.shadowBlur),foregroundImageShadowColor:t.foregroundImageShadowColor||(null===(j=t.foreground)||void 0===j||null===(H=j.image)||void 0===H?void 0:H.shadowColor),foregroundPadding:t.foregroundPadding,positionProbeBackgroundColor:t.positionProbeBackgroundColor||(null===(X=t.positionProbe)||void 0===X?void 0:X.backgroundColor)||(null===(Y=t.positionDetection)||void 0===Y?void 0:Y.backgroundColor),positionProbeForegroundColor:t.positionProbeForegroundColor||(null===(G=t.positionProbe)||void 0===G?void 0:G.foregroundColor)||(null===(W=t.positionDetection)||void 0===W?void 0:W.foregroundColor),separatorColor:t.separatorColor||(null===(K=t.separator)||void 0===K?void 0:K.color),positionAdjustBackgroundColor:t.positionAdjustBackgroundColor||(null===(Q=t.positionAdjust)||void 0===Q?void 0:Q.backgroundColor)||(null===(U=t.alignment)||void 0===U?void 0:U.backgroundColor),positionAdjustForegroundColor:t.positionAdjustForegroundColor||(null===(J=t.positionAdjust)||void 0===J?void 0:J.foregroundColor)||(null===(q=t.alignment)||void 0===q?void 0:q.foregroundColor),timingBackgroundColor:t.timingBackgroundColor||(null===($=t.timing)||void 0===$?void 0:$.backgroundColor),timingForegroundColor:t.timingForegroundColor||(null===(V=t.timing)||void 0===V?void 0:V.foregroundColor),typeNumberBackgroundColor:t.typeNumberBackgroundColor||(null===(Z=t.typeNumber)||void 0===Z?void 0:Z.backgroundColor)||(null===(tt=t.versionInformation)||void 0===tt?void 0:tt.backgroundColor),typeNumberForegroundColor:t.typeNumberForegroundColor||(null===(et=t.typeNumber)||void 0===et?void 0:et.foregroundColor)||(null===(ot=t.versionInformation)||void 0===ot?void 0:ot.foregroundColor),darkBlockColor:t.darkBlockColor||(null===(rt=t.darkBlock)||void 0===rt?void 0:rt.color)},!0))},f.prototype.make=function(){var t=this.foregroundColor,o=this.backgroundColor,r=this.typeNumber,n=this.errorCorrectLevel,i=this.data,a=this.dataEncode,u=this.size,s=this.margin,d=this.useDynamicSize;if(t===o)throw console.error("[uQRCode]: foregroundColor and backgroundColor cannot be the same!"),new f.Error("foregroundColor and backgroundColor cannot be the same!");a&&(i=function(t){t=t.toString();for(var e,o="",r=0;r<t.length;r++)(e=t.charCodeAt(r))>=1&&e<=127?o+=t.charAt(r):e>2047?(o+=String.fromCharCode(224|e>>12&15),o+=String.fromCharCode(128|e>>6&63),o+=String.fromCharCode(128|e>>0&63)):(o+=String.fromCharCode(192|e>>6&31),o+=String.fromCharCode(128|e>>0&63));return o}(i));var c=new e(r,n);c.addData(i),c.make(),this.base=c,this.typeNumber=c.typeNumber,this.modules=c.modules,this.moduleCount=c.moduleCount,this.dynamicSize=d?Math.ceil((u-2*s)/c.moduleCount)*c.moduleCount+2*s:u,function(t){var e=t.dynamicSize,o=t.margin,r=t.backgroundColor,n=t.backgroundPadding,i=t.foregroundColor,a=t.foregroundPadding,u=t.modules,s=t.moduleCount,d=(e-2*o)/s,c=d,l=0;n>0&&(c-=2*(l=c*n/2));var g=d,h=0;a>0&&(g-=2*(h=g*a/2));for(var f=0;f<s;f++)for(var m=0;m<s;m++){var p=m*d+o,v=f*d+o;if(u[f][m]){var b=h,y=p+h,C=v+h,w=g,k=g;u[f][m]={type:["foreground"],color:i,isBlack:!0,isDrawn:!1,destX:p,destY:v,destWidth:d,destHeight:d,x:y,y:C,width:w,height:k,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b}}else b=l,y=p+l,C=v+l,w=c,k=c,u[f][m]={type:["background"],color:r,isBlack:!1,isDrawn:!1,destX:p,destY:v,destWidth:d,destHeight:d,x:y,y:C,width:w,height:k,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b}}}(this),function(t){var e=t.modules,o=t.moduleCount,r=t.positionProbeBackgroundColor,n=t.positionProbeForegroundColor,i=o-7;[[0,0,1],[1,0,1],[2,0,1],[3,0,1],[4,0,1],[5,0,1],[6,0,1],[0,1,1],[1,1,0],[2,1,0],[3,1,0],[4,1,0],[5,1,0],[6,1,1],[0,2,1],[1,2,0],[2,2,1],[3,2,1],[4,2,1],[5,2,0],[6,2,1],[0,3,1],[1,3,0],[2,3,1],[3,3,1],[4,3,1],[5,3,0],[6,3,1],[0,4,1],[1,4,0],[2,4,1],[3,4,1],[4,4,1],[5,4,0],[6,4,1],[0,5,1],[1,5,0],[2,5,0],[3,5,0],[4,5,0],[5,5,0],[6,5,1],[0,6,1],[1,6,1],[2,6,1],[3,6,1],[4,6,1],[5,6,1],[6,6,1]].forEach((function(t){var o=e[t[0]][t[1]],a=e[t[0]+i][t[1]],u=e[t[0]][t[1]+i];u.type.push("positionProbe"),a.type.push("positionProbe"),o.type.push("positionProbe"),o.color=1==t[2]?n:r,a.color=1==t[2]?n:r,u.color=1==t[2]?n:r}))}(this),function(t){var e=t.modules,o=t.moduleCount,r=t.separatorColor;[[7,0],[7,1],[7,2],[7,3],[7,4],[7,5],[7,6],[7,7],[0,7],[1,7],[2,7],[3,7],[4,7],[5,7],[6,7]].forEach((function(t){var n=e[t[0]][t[1]],i=e[o-t[0]-1][t[1]],a=e[t[0]][o-t[1]-1];a.type.push("separator"),i.type.push("separator"),n.type.push("separator"),n.color=r,i.color=r,a.color=r}))}(this),function(t){var e=t.typeNumber,o=t.modules,r=t.moduleCount,n=t.foregroundColor,i=t.backgroundColor,a=t.positionAdjustForegroundColor,u=t.positionAdjustBackgroundColor,s=t.timingForegroundColor,d=t.timingBackgroundColor,c=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]][e-1];if(c)for(var l=[[-2,-2,1],[-1,-2,1],[0,-2,1],[1,-2,1],[2,-2,1],[-2,-1,1],[-1,-1,0],[0,-1,0],[1,-1,0],[2,-1,1],[-2,0,1],[-1,0,0],[0,0,1],[1,0,0],[2,0,1],[-2,1,1],[-1,1,0],[0,1,0],[1,1,0],[2,1,1],[-2,2,1],[-1,2,1],[0,2,1],[1,2,1],[2,2,1]],g=c.length,h=0;h<g;h++)for(var f=0;f<g;f++){var m={x:c[h],y:c[f]},p=m.x,v=m.y;p<9&&v<9||p>r-9-1&&v<9||v>r-9-1&&p<9||l.forEach((function(t){var e=o[p+t[0]][v+t[1]];e.type.push("positionAdjust"),e.type.includes("timing")?1==t[2]?e.color=a==n?s:a:e.color=a==n&&u==i?d:u:e.color=1==t[2]?a:u}))}}(this),function(t){for(var e=t.modules,o=t.moduleCount,r=t.timingForegroundColor,n=t.timingBackgroundColor,i=o-16,a=0;a<i;a++){var u=e[6][8+a],s=e[8+a][6];u.type.push("timing"),s.type.push("timing"),u.color=1&a^1?r:n,s.color=1&a^1?r:n}}(this),function(t){var e=t.modules,o=t.moduleCount,r=t.darkBlockColor,n=e[o-7-1][8];n.type.push("darkBlock"),n.color=r}(this),function(t){var e=t.typeNumber,o=t.modules,r=t.moduleCount,n=t.typeNumberBackgroundColor,i=t.typeNumberForegroundColor;if(e<7)return o;var a=[0,0,0,0,0,0,0,"000111110010010100","001000010110111100","001001101010011001","001010010011010011","001011101111110110","001100011101100010","001101100001000111","001110011000001101","001111100100101000","010000101101111000","010001010001011101","010010101000010111","010011010100110010","010100100110100110","010101011010000011","010110100011001001","010111011111101100","011000111011000100","011001000111100001","011010111110101011","011011000010001110","011100110000011010","011101001100111111","011110110101110101","011111001001010000","100000100111010101","100001011011110000","100010100010111010","100011011110011111","100100101100001011","100101010000101110","100110101001100100","100111010101000001","101000110001101001"],u=a[e]+a[e],s=[r-11,r-10,r-9];[[5,s[2]],[5,s[1]],[5,s[0]],[4,s[2]],[4,s[1]],[4,s[0]],[3,s[2]],[3,s[1]],[3,s[0]],[2,s[2]],[2,s[1]],[2,s[0]],[1,s[2]],[1,s[1]],[1,s[0]],[0,s[2]],[0,s[1]],[0,s[0]],[s[2],5],[s[1],5],[s[0],5],[s[2],4],[s[1],4],[s[0],4],[s[2],3],[s[1],3],[s[0],3],[s[2],2],[s[1],2],[s[0],2],[s[2],1],[s[1],1],[s[0],1],[s[2],0],[s[1],0],[s[0],0]].forEach((function(t,e){var r=o[t[0]][t[1]];r.type.push("typeNumber"),r.color="1"==u[e]?i:n}))}(this),this.isMaked=!0,this.drawModules=[]},f.prototype.getDrawModules=function(){if(this.drawModules&&this.drawModules.length>0)return this.drawModules;var t=this.drawModules=[],e=this.modules,o=this.moduleCount,r=this.dynamicSize,n=this.areaColor,i=this.backgroundImageSrc,a=this.backgroundImageX,u=this.backgroundImageY,s=this.backgroundImageWidth,d=this.backgroundImageHeight,c=this.backgroundImageAlpha,l=this.backgroundImageBorderRadius,g=this.foregroundImageSrc,h=this.foregroundImageX,f=this.foregroundImageY,m=this.foregroundImageWidth,p=this.foregroundImageHeight,v=this.foregroundImagePadding,b=this.foregroundImageBackgroundColor,y=this.foregroundImageBorderRadius,C=this.foregroundImageShadowOffsetX,w=this.foregroundImageShadowOffsetY,k=this.foregroundImageShadowBlur,I=this.foregroundImageShadowColor;n&&t.push({name:"area",type:"area",color:n,x:0,y:0,width:r,height:r}),i&&t.push({name:"backgroundImage",type:"image",imageSrc:i,mappingName:"backgroundImageSrc",x:a,y:u,width:s,height:d,alpha:c,borderRadius:l});for(var B=0;B<o;B++)for(var S=0;S<o;S++){var x=e[B][S];x.isDrawn||(x.type.includes("foreground")?t.push({name:"foreground",type:"tile",color:x.color,destX:x.destX,destY:x.destY,destWidth:x.destWidth,destHeight:x.destHeight,x:x.x,y:x.y,width:x.width,height:x.height,paddingTop:x.paddingTop,paddingRight:x.paddingRight,paddingBottom:x.paddingBottom,paddingLeft:x.paddingLeft,rowIndex:B,colIndex:S}):t.push({name:"background",type:"tile",color:x.color,destX:x.destX,destY:x.destY,destWidth:x.destWidth,destHeight:x.destHeight,x:x.x,y:x.y,width:x.width,height:x.height,paddingTop:x.paddingTop,paddingRight:x.paddingRight,paddingBottom:x.paddingBottom,paddingLeft:x.paddingLeft,rowIndex:B,colIndex:S}),x.isDrawn=!0)}return g&&t.push({name:"foregroundImage",type:"image",imageSrc:g,mappingName:"foregroundImageSrc",x:h,y:f,width:m,height:p,padding:v,backgroundColor:b,borderRadius:y,shadowOffsetX:C,shadowOffsetY:w,shadowBlur:k,shadowColor:I}),t},f.prototype.isBlack=function(t,e){var o=this.moduleCount;return!(0>t||0>e||t>=o||e>=o)&&this.modules[t][e].isBlack},f.prototype.drawCanvas=function(t){var e=this,o=this.isMaked,r=this.canvasContext,n=(this.useDynamicSize,this.dynamicSize,this.foregroundColor,this.foregroundPadding,this.backgroundColor,this.backgroundPadding,this.drawReserve);this.margin;if(!o)return console.error("[uQRCode]: please execute the make method first!"),Promise.reject(new f.Error("please execute the make method first!"));var i=this.getDrawModules(),s=function(){var o=u(a().mark((function o(u,s){var d,c,l,g,h,m,p,v,b,y,C,w,k,I;return a().wrap((function(o){while(1)switch(o.prev=o.next){case 0:o.prev=0,r.draw(t),d=0;case 3:if(!(d<i.length)){o.next=48;break}c=i[d],o.t0=(r.save(),c.type),o.next="area"===o.t0?8:"tile"===o.t0?10:"image"===o.t0?13:44;break;case 8:return r.setFillStyle(c.color),r.fillRect(c.x,c.y,c.width,c.height),o.abrupt("break",44);case 10:return l=c.x,g=c.y,h=c.width,m=c.height,r.setFillStyle(c.color),r.fillRect(l,g,h,m),o.abrupt("break",44);case 13:if("backgroundImage"!==c.name){o.next=28;break}return l=Math.round(c.x),g=Math.round(c.y),h=Math.round(c.width),m=Math.round(c.height),h<2*(v=Math.round(c.borderRadius))&&(v=h/2),m<2*v&&(v=m/2),r.setGlobalAlpha(c.alpha),v>0&&(r.beginPath(),r.moveTo(l+v,g),r.arcTo(l+h,g,l+h,g+m,v),r.arcTo(l+h,g+m,l,g+m,v),r.arcTo(l,g+m,l,g,v),r.arcTo(l,g,l+h,g,v),r.closePath(),r.setStrokeStyle("rgba(0,0,0,0)"),r.stroke(),r.clip()),o.prev=16,o.next=19,e.loadImage(c.imageSrc);case 19:p=o.sent,r.drawImage(p,l,g,h,m),o.next=26;break;case 23:throw o.prev=23,o.t1=o["catch"](16),console.error("[uQRCode]: ".concat(c.mappingName," invalid!")),new f.Error("".concat(c.mappingName," invalid!"));case 26:o.next=44;break;case 28:if("foregroundImage"!==c.name){o.next=44;break}return l=Math.round(c.x),g=Math.round(c.y),h=Math.round(c.width),m=Math.round(c.height),b=Math.round(c.padding),h<2*(v=Math.round(c.borderRadius))&&(v=h/2),m<2*v&&(v=m/2),y=l-b,C=g-b,w=h+2*b,k=m+2*b,I=Math.round(w/h*v),w<2*I&&(I=w/2),k<2*I&&(I=k/2),r.save(),r.setShadow(c.shadowOffsetX,c.shadowOffsetY,c.shadowBlur,c.shadowColor),I>0?(r.beginPath(),r.moveTo(y+I,C),r.arcTo(y+w,C,y+w,C+k,I),r.arcTo(y+w,C+k,y,C+k,I),r.arcTo(y,C+k,y,C,I),r.arcTo(y,C,y+w,C,I),r.closePath(),r.setFillStyle(c.backgroundColor),r.fill()):(r.setFillStyle(c.backgroundColor),r.fillRect(y,C,w,k)),r.restore(),r.save(),I>0?(r.beginPath(),r.moveTo(y+I,C),r.arcTo(y+w,C,y+w,C+k,I),r.arcTo(y+w,C+k,y,C+k,I),r.arcTo(y,C+k,y,C,I),r.arcTo(y,C,y+w,C,I),r.closePath(),r.setFillStyle(b>0?c.backgroundColor:"rgba(0,0,0,0)"),r.fill()):(r.setFillStyle(b>0?c.backgroundColor:"rgba(0,0,0,0)"),r.fillRect(y,C,w,k)),r.restore(),v>0&&(r.beginPath(),r.moveTo(l+v,g),r.arcTo(l+h,g,l+h,g+m,v),r.arcTo(l+h,g+m,l,g+m,v),r.arcTo(l,g+m,l,g,v),r.arcTo(l,g,l+h,g,v),r.closePath(),r.setStrokeStyle("rgba(0,0,0,0)"),r.stroke(),r.clip()),o.prev=34,o.next=37,e.loadImage(c.imageSrc);case 37:p=o.sent,r.drawImage(p,l,g,h,m),o.next=44;break;case 41:throw o.prev=41,o.t2=o["catch"](34),console.error("[uQRCode]: ".concat(c.mappingName," invalid!")),new f.Error("".concat(c.mappingName," invalid!"));case 44:n&&r.draw(!0),r.restore();case 45:d++,o.next=3;break;case 48:r.draw(!0),setTimeout(u,150),o.next=54;break;case 51:o.prev=51,o.t3=o["catch"](0),s(o.t3);case 54:case"end":return o.stop()}}),o,null,[[0,51],[16,23],[34,41]])})));return function(t,e){return o.apply(this,arguments)}}();return new Promise((function(t,e){s(t,e)}))},f.prototype.draw=function(t){return this.drawCanvas(t)},f.prototype.register=function(t){t&&t(f,this,!0)},f}))}).call(this,o("0ee4"))},4550:function(t,e,o){o("6a54");var r=o("8bcf");t.exports=function(t,e,o){return e=r(e),e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"46c5":function(t,e,o){function r(t,e,o,r,n,i,a){try{var u=t[i](a),s=u.value}catch(d){return void o(d)}u.done?e(s):Promise.resolve(s).then(r,n)}o("bf0f"),t.exports=function(t){return function(){var e=this,o=arguments;return new Promise((function(n,i){var a=t.apply(e,o);function u(t){r(a,n,i,u,s,"next",t)}function s(t){r(a,n,i,u,s,"throw",t)}u(void 0)}))}},t.exports.__esModule=!0,t.exports["default"]=t.exports},5014:function(t,e,o){o("dc8a"),o("01a2"),o("8f71"),o("bf0f"),o("9a2c"),o("aa9c"),o("2797"),o("a644"),o("a03a"),o("6a54");var r=o("4550");function n(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,r)}return o}t.exports=function(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?n(Object(o),!0).forEach((function(e){r(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t},t.exports.__esModule=!0,t.exports["default"]=t.exports},5381:function(t,e,o){"use strict";o.r(e);var r=o("c368"),n=o("180f");for(var i in n)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(i);o("e4db");var a=o("828b"),u=Object(a["a"])(n["default"],r["b"],r["c"],!1,null,"8086cc36",null,!1,r["a"],void 0);e["default"]=u.exports},"56c9":function(t,e,o){o("9e15"),o("884b"),o("01a2"),o("e39c"),o("bf0f"),o("7a76"),o("c9b5"),o("64aa");var r=o("bdbb")["default"];t.exports=function(t,e){if("object"!==r(t)||null===t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||"default");if("object"!==r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports},"71c5":function(t,e,o){"use strict";o("6a54");var r=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(o("4151")),i={data:function(){return{pageHeight:0,phhone:""}},mounted:function(){this.generateQRCode();var t=uni.getSystemInfoSync();this.pageHeight=t.screenHeight-t.windowTop-t.statusBarHeight},onLoad:function(){this.phone=uni.getStorageSync("userInfo").phone},methods:{generateQRCode:function(){var t=uni.getStorageSync("userInfo"),e=new n.default;e.data="http://**************:80/#/pages/login/register_2?phone="+t.phone,e.size=150,e.backgroundColor="rgba(255,255,255,0)",e.foregroundColor="rgba(0,0,0,1)",e.make();var o=uni.createCanvasContext("qrcode",this);e.canvasContext=o,e.drawCanvas()},copyText:function(t){navigator.clipboard.writeText(t).then((function(){console.log("文本已成功复制到剪贴板")})).catch((function(t){console.error("无法复制文本：",t)}))}}};e.default=i},"8bcf":function(t,e,o){var r=o("bdbb")["default"],n=o("56c9");t.exports=function(t){var e=n(t,"string");return"symbol"===r(e)?e:String(e)},t.exports.__esModule=!0,t.exports["default"]=t.exports},c368:function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-uni-view",{staticClass:"code",style:{height:t.pageHeight+"px"}},[o("v-uni-canvas",{staticClass:"qrcode",staticStyle:{width:"150px",height:"150px"},attrs:{"canvas-id":"qrcode"}}),o("v-uni-text",[t._v("分享链接:"),o("b",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copyText("http://localhost:8080/#/pages/login/register_2?phone="+this.phone)}}},[t._v("http://localhost:8080/#/pages/login/register_2?phone="+t._s(this.phone))])])],1)},n=[]},dc77:function(t,e,o){var r=o("3f02");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=o("967d").default;n("6b4628eb",r,!0,{sourceMap:!1,shadowMode:!1})},dd7e:function(t,e,o){o("6a54"),o("01a2"),o("e39c"),o("bf0f"),o("844d"),o("18f7"),o("de6c"),o("3872e"),o("4e9b"),o("114e"),o("c240"),o("926e"),o("7a76"),o("c9b5"),o("aa9c"),o("2797"),o("8a8d"),o("dc69"),o("f7a5");var r=o("bdbb")["default"];function n(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=n=function(){return e},t.exports.__esModule=!0,t.exports["default"]=t.exports;var e={},o=Object.prototype,i=o.hasOwnProperty,a=Object.defineProperty||function(t,e,o){t[e]=o.value},u="function"==typeof Symbol?Symbol:{},s=u.iterator||"@@iterator",d=u.asyncIterator||"@@asyncIterator",c=u.toStringTag||"@@toStringTag";function l(t,e,o){return Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(T){l=function(t,e,o){return t[e]=o}}function g(t,e,o,r){var n=e&&e.prototype instanceof m?e:m,i=Object.create(n.prototype),u=new L(r||[]);return a(i,"_invoke",{value:B(t,o,u)}),i}function h(t,e,o){try{return{type:"normal",arg:t.call(e,o)}}catch(T){return{type:"throw",arg:T}}}e.wrap=g;var f={};function m(){}function p(){}function v(){}var b={};l(b,s,(function(){return this}));var y=Object.getPrototypeOf,C=y&&y(y(E([])));C&&C!==o&&i.call(C,s)&&(b=C);var w=v.prototype=m.prototype=Object.create(b);function k(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){var o;a(this,"_invoke",{value:function(n,a){function u(){return new e((function(o,u){(function o(n,a,u,s){var d=h(t[n],t,a);if("throw"!==d.type){var c=d.arg,l=c.value;return l&&"object"==r(l)&&i.call(l,"__await")?e.resolve(l.__await).then((function(t){o("next",t,u,s)}),(function(t){o("throw",t,u,s)})):e.resolve(l).then((function(t){c.value=t,u(c)}),(function(t){return o("throw",t,u,s)}))}s(d.arg)})(n,a,o,u)}))}return o=o?o.then(u,u):u()}})}function B(t,e,o){var r="suspendedStart";return function(n,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===n)throw i;return _()}for(o.method=n,o.arg=i;;){var a=o.delegate;if(a){var u=S(a,o);if(u){if(u===f)continue;return u}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if("suspendedStart"===r)throw r="completed",o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r="executing";var s=h(t,e,o);if("normal"===s.type){if(r=o.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(r="completed",o.method="throw",o.arg=s.arg)}}}function S(t,e){var o=e.method,r=t.iterator[o];if(void 0===r)return e.delegate=null,"throw"===o&&t.iterator["return"]&&(e.method="return",e.arg=void 0,S(t,e),"throw"===e.method)||"return"!==o&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+o+"' method")),f;var n=h(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,f;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function E(t){if(t){var e=t[s];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(i.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:_}}function _(){return{value:void 0,done:!0}}return p.prototype=v,a(w,"constructor",{value:v,configurable:!0}),a(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,c,"GeneratorFunction")),t.prototype=Object.create(w),t},e.awrap=function(t){return{__await:t}},k(I.prototype),l(I.prototype,d,(function(){return this})),e.AsyncIterator=I,e.async=function(t,o,r,n,i){void 0===i&&(i=Promise);var a=new I(g(t,o,r,n),i);return e.isGeneratorFunction(o)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(w),l(w,c,"Generator"),l(w,s,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),o=[];for(var r in e)o.push(r);return o.reverse(),function t(){for(;o.length;){var r=o.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=E,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(o,r){return a.type="throw",a.arg=t,e.next=o,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r],a=n.completion;if("root"===n.tryLoc)return o("end");if(n.tryLoc<=this.prev){var u=i.call(n,"catchLoc"),s=i.call(n,"finallyLoc");if(u&&s){if(this.prev<n.catchLoc)return o(n.catchLoc,!0);if(this.prev<n.finallyLoc)return o(n.finallyLoc)}else if(u){if(this.prev<n.catchLoc)return o(n.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return o(n.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var n=r;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),P(o),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc===t){var r=o.completion;if("throw"===r.type){var n=r.arg;P(o)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,o){return this.delegate={iterator:E(t),resultName:e,nextLoc:o},"next"===this.method&&(this.arg=void 0),f}},e}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},e4db:function(t,e,o){"use strict";var r=o("dc77"),n=o.n(r);n.a}}]);