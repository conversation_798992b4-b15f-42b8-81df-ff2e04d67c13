(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-orderDetail"],{1701:function(t,i,e){"use strict";var n=e("64a4"),a=e.n(n);a.a},"4fa2":function(t,i,e){"use strict";e.r(i);var n=e("f859"),a=e("ac46");for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(s);e("1701");var o=e("828b"),c=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"ccc5f926",null,!1,n["a"],void 0);i["default"]=c.exports},"64a4":function(t,i,e){var n=e("7181");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=e("967d").default;a("75c69703",n,!0,{sourceMap:!1,shadowMode:!1})},7181:function(t,i,e){var n=e("c86c");i=n(!1),i.push([t.i,".container[data-v-ccc5f926]{padding:%?20?%;background-color:#f5f5f5}.header[data-v-ccc5f926]{display:flex;justify-content:space-between;align-items:center;background-color:#3cb371;padding:%?10?% %?20?%;color:#fff}.title[data-v-ccc5f926]{font-size:%?32?%}.order-info[data-v-ccc5f926],\n.fee-info[data-v-ccc5f926],\n.payment-options[data-v-ccc5f926]{background-color:#fff;padding:%?20?%;margin-top:%?20?%;border-radius:%?10?%}.info-row[data-v-ccc5f926],\n.option-row[data-v-ccc5f926]{display:flex;justify-content:space-between;margin-top:%?50?%;margin-bottom:%?10?%}.label[data-v-ccc5f926]{color:#555;font-size:%?28?%}.value[data-v-ccc5f926]{color:#333;font-size:%?28?%}.fee-text[data-v-ccc5f926]{color:red;font-size:%?28?%}.fee-detail[data-v-ccc5f926]{color:#666;margin-top:%?10?%}.submit-btn[data-v-ccc5f926]{width:100%;background-color:#3cb371;color:#fff;padding:%?15?%;border-radius:%?10?%;font-size:%?28?%;margin-top:%?20?%}.text[data-v-ccc5f926]{height:%?60?%;margin-top:%?10?%;line-height:%?60?%;padding-left:%?30?%;font-weight:700}",""]),t.exports=i},ac46:function(t,i,e){"use strict";e.r(i);var n=e("b6f0"),a=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(s);i["default"]=a.a},b6f0:function(t,i,e){"use strict";e("6a54");var n=e("f5bd").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=n(e("2634")),s=n(e("9b1b")),o=n(e("2fdc"));e("8f71"),e("bf0f"),e("c223"),e("fd3c"),e("aa9c"),e("dd2b");var c={data:function(){return{order:{},paymentOptions:{balance:!1},balance:"",userInfo:{},fileList:[],storeName:"",price:"",bank:{},chat:{},alipay:{},flag:0,submitLoading:!1}},onLoad:function(t){console.log(t.id),this.api_get(t.id),this.userInfo=uni.getStorageSync("userInfo"),this.storeName=t.storename,this.price=t.price,this.api_getpay(),this.getuserInfo()},methods:{getuserInfo:function(){var t=this;this.$api.request({url:this.$api.getuserInfo+"/".concat(this.userInfo.id)}).then((function(i){t.balance=i.data.balance}))},goBack:function(){uni.navigateBack()},api_get:function(t){var i=this;this.$api.request({url:this.$api.getseildetial+"/".concat(t)}).then((function(t){console.log(t,"数据"),i.order=t.data}))},api_getpay:function(){var t=this;this.$api.request({url:this.$api.getpayways+"?userId=1"}).then((function(i){console.log(i.rows,"付款方式"),t.bank=i.rows.filter((function(t){return 1==t.methodId}))[0],t.chat=i.rows.filter((function(t){return 2==t.methodId}))[0],t.alipay=i.rows.filter((function(t){return 3==t.methodId}))[0]}))},afterRead:function(t){var i=this;return(0,o.default)((0,a.default)().mark((function e(){var n,o,c,r,l;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=[].concat(t.file),o=i.fileList.length,n.map((function(t){i.fileList.push((0,s.default)((0,s.default)({},t),{},{status:"uploading",message:"上传中"}))})),c=0;case 4:if(!(c<n.length)){e.next=15;break}return e.next=7,i.$api.uploadFile({url:i.$api.upPic,filePath:n[c].url});case 7:r=e.sent,r=JSON.parse(r),l=i.fileList[o],i.fileList.splice(o,1,Object.assign(l,{status:"success",message:"",url:r.url})),o++;case 12:c++,e.next=4;break;case 15:case"end":return e.stop()}}),e)})))()},check:function(){var t=[];t[0]=this.chat.chatImg,console.log("imgsArray[0]",t[0]),uni.previewImage({current:0,urls:t})},check2:function(){var t=[];t[0]=this.alipay.alipayImg,console.log("imgsArray[0]",t[0]),uni.previewImage({current:0,urls:t})},deletePic:function(t){this.fileList.splice(t.index,1)},useBalance:function(t){this.paymentOptions.balance=!this.paymentOptions.balance,0==this.paymentOptions.balance?this.flag=0:this.balance>=this.order.moneyNum?this.flag=2:this.flag=1},submitOrder:function(){var t=this;this.validateOrderData()&&(this.submitLoading=!0,console.log("提交订单信息：",this.paymentOptions),this.calculatePaymentFlag(),uni.showModal({title:"操作通知",content:"是否确认发布?",success:function(i){i.confirm?t.processOrderSubmission():t.submitLoading=!1},fail:function(){t.submitLoading=!1}}))},validateOrderData:function(){return this.order&&this.order.id?!(!this.userInfo||!this.userInfo.id)||(uni.showToast({title:"用户信息不完整",icon:"none"}),!1):(uni.showToast({title:"订单信息不完整",icon:"none"}),!1)},calculatePaymentFlag:function(){0==this.paymentOptions.balance?this.flag=0:this.balance>=this.order.moneyNum?this.flag=2:this.flag=1},processOrderSubmission:function(){if(console.log(this.fileList,"fileList"),2!=this.flag&&0==this.fileList.length)return uni.showToast({title:"请上传支付凭证",icon:"none"}),void(this.submitLoading=!1);var t=this.prepareRequestData();this.sendSubmitRequest(t)},prepareRequestData:function(){var t={orderId:this.order.id,userId:this.userInfo.id,goodsId:this.order.goodsId,price:this.order.moneyNum,type:this.flag};if(2!=this.flag&&this.fileList.length>0){var i=this.fileList.map((function(t){return t.url})).join(",");t.uploadImg=i,console.log(i,"修改的图片格式")}return t},sendSubmitRequest:function(t){var i=this;this.$api.request({url:this.$api.putmessionon,method:"POST",data:t}).then((function(t){i.handleSubmitSuccess(t)})).catch((function(t){i.handleSubmitError(t)})).finally((function(){i.submitLoading=!1}))},handleSubmitSuccess:function(t){console.log("提交响应:",t),200==t.code?uni.showModal({showCancel:!1,content:"发布成功",success:function(t){t.confirm&&uni.navigateBack()}}):uni.showToast({title:t.msg||"发布失败，请重试",icon:"none"})},handleSubmitError:function(t){console.error("提交订单失败:",t),uni.showToast({title:"网络错误，请检查网络连接后重试",icon:"none"})}}};i.default=c},f859:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){return n}));var n={uUpload:e("760b").default},a=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"header"},[e("v-uni-text",{staticClass:"title"},[t._v("委托寄售")])],1),e("v-uni-view",{staticClass:"order-info"},[e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"label"},[t._v("商品名称：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(t.storeName))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"label"},[t._v("原价：")]),e("v-uni-text",{staticClass:"value"},[t._v("¥ "+t._s(t.price))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"label"},[t._v("现价：")]),e("v-uni-text",{staticClass:"value"},[t._v("¥ "+t._s(t.order.price))])],1),e("v-uni-view",{staticClass:"info-row"},[e("v-uni-text",{staticClass:"label"},[t._v("数量：")]),e("v-uni-text",{staticClass:"value"},[t._v("1")])],1),e("v-uni-view",[e("v-uni-view",[t._v("收款方式：")]),e("v-uni-view",{staticStyle:{border:"1rpx solid #000",width:"650rpx",padding:"20rpx","margin-top":"20rpx"}},[e("v-uni-view",[t._v("银行名称："+t._s(t.bank.bankName))]),e("v-uni-view",[t._v("银行卡号："+t._s(t.bank.accountInfo))])],1),e("v-uni-view",{on:{cllick:function(i){arguments[0]=i=t.$handleEvent(i),t.previewImg(1)}}},[e("v-uni-view",[t._v("微信收款码：")]),e("v-uni-view",{staticStyle:{width:"200rpx",height:"200rpx"}},[e("img",{staticStyle:{widtn:"100%",height:"100%"},attrs:{src:t.chat.chatImg,alt:""},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.check.apply(void 0,arguments)}}})])],1),e("v-uni-view",{on:{cllick:function(i){arguments[0]=i=t.$handleEvent(i),t.previewImg(2)}}},[e("v-uni-view",[t._v("支付宝收款码：")]),e("v-uni-view",{staticStyle:{width:"200rpx",height:"200rpx"}},[e("img",{staticStyle:{widtn:"100%",height:"100%"},attrs:{src:t.alipay.alipayImg,alt:""},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.check2.apply(void 0,arguments)}}})])],1)],1)],1),e("v-uni-view",{staticClass:"fee-info"},[e("v-uni-text",{staticClass:"fee-text"},[t._v("根据《寄售规则》您需支付寄售价格的 3.5% 作为手续费：")]),e("v-uni-text",{staticClass:"fee-detail"},[t._v("委托价格:¥"+t._s(t.order.price)+"，需支付手续费:¥"+t._s(t.order.moneyNum))])],1),e("v-uni-view",{staticClass:"payment-options"},[e("v-uni-view",{staticClass:"option-row"},[0!=t.balance?e("v-uni-checkbox",{attrs:{checked:t.paymentOptions.balance},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.useBalance.apply(void 0,arguments)}}}):t._e(),t._v("可用余额：￥"+t._s(t.balance))],1),t.paymentOptions.balance?e("v-uni-view",{staticClass:"text"},[t._v("已抵扣：-￥"),e("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(t.balance>=this.order.moneyNum?this.order.moneyNum:t.balance))])],1):t._e(),t.paymentOptions.balance?e("v-uni-view",{staticClass:"text"},[t._v("需付款：￥"),e("v-uni-text",{staticStyle:{color:"red"}},[t._v(t._s(this.order.moneyNum-t.balance<0?0:this.order.moneyNum-t.balance))])],1):t._e(),2!=t.flag?e("v-uni-view",{staticClass:"option-row"},[e("v-uni-view",{staticClass:"left"},[t._v("付款凭证：")]),e("v-uni-view",{staticClass:"right"},[e("u-upload",{attrs:{fileList:t.fileList,multiple:!0},on:{afterRead:function(i){arguments[0]=i=t.$handleEvent(i),t.afterRead.apply(void 0,arguments)},delete:function(i){arguments[0]=i=t.$handleEvent(i),t.deletePic.apply(void 0,arguments)}}})],1)],1):t._e()],1),e("v-uni-view",{staticClass:"action"},[e("v-uni-button",{staticClass:"submit-btn",attrs:{loading:t.submitLoading},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.submitOrder.apply(void 0,arguments)}}},[t._v("确认发布")])],1)],1)},s=[]}}]);