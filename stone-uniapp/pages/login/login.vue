<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="header">
			<image class="logo" src="/static/icon/biglogo.png" />
		</view>

		<!-- 登录切换按钮 -->
		<!-- <view class="tab-switch">
			<button :class="{ active: currentTab === 'account' }" @click="switchTab('account')">账号密码登录</button>
			<button :class="{ active: currentTab === 'phone' }" @click="switchTab('phone')">手机号验证码登录</button>
		</view> -->

		<!-- 登录表单 -->
		<view v-if="currentTab === 'account'" class="form">
			<!-- 账号密码登录 -->
			<view>用户名：</view>
			<input v-model="form.phone" class="input" placeholder="请输入账号" />
			<view>密&emsp;码：</view>
			<input v-model="form.password" class="input" type="password" placeholder="请输入密码" />
			<label style="padding:20rpx 0;">
				<checkbox @click="setcp" :checked="setcpflag" /><text>记住密码</text>
			</label>
			<button class="login-btn" @click="login">登录</button>
		</view>

		<view v-else-if="currentTab === 'phone'" class="form">
			<!-- 手机号验证码登录 -->
			<input v-model="form.phone" class="input" placeholder="请输入手机号" />
			<view class="code-input">
				<input v-model="form.code" class="input" placeholder="请输入验证码" />
				<button class="send-code-btn" @click="sendCode" :disabled="countdown > 0">
					{{ countdown > 0 ? countdown + '秒后重新发送' : '发送验证码' }}
				</button>
			</view>
			<button class="login-btn" @click="login">登录</button>
		</view>
		<!-- 微信快捷登录 -->
		<view class="quick-login" v-if="false">
			<text>其他方式</text>
			<button class="wechat-login-btn" @click="wechatLogin">微信快捷登录</button>
		</view>
		<!-- 用户协议 -->
		<view class="agreement">
			<text>登录即表示同意</text>
			<text class="link" @click="viewTerms">《会员协议》</text>
			<text>与</text>
			<text class="link" @click="viewPrivacy">《隐私政策》</text>
			<!-- <text>-></text>
			<button class="link link_btn" @click="viewRegister">去注册</button> -->
		</view>
	</view>
</template>

<script>
	import {
		mapMutations
	} from 'vuex';
	export default {
		data() {
			return {
				currentTab: 'account', // 当前选中的登录方式
				countdown: 0, // 验证码倒计时
				form: {
					password: '',
					phone: '',
					code: '',
				},
				setcpflag: false
			};
		},
		onLoad() {
			this.getsavedata()
		},
		onShow() {
			this.getsavedata()
		},
		methods: {
			// 切换登录方式
			switchTab(tab) {
				this.currentTab = tab;
			},
			...mapMutations(['setUserCountKey']),
			// 登录操作
			login() {
				console.log(this.form);
				if (this.currentTab === 'account') {
					if (!this.form.phone || !this.form.password) {
						return uni.showToast({
							title: '请输入账号和密码',
							icon: 'none'
						});
					}
					const data = {
						phone: this.form.phone,
						password: this.form.password,
						loginType: "1"
					}

					// 调用后端登录接口
					this.apiEvent(data)
				} else if (this.currentTab === 'phone') {
					if (!this.form.phone || !this.form.code) {
						return uni.showToast({
							title: '请输入手机号和验证码',
							icon: 'none'
						});
					}
					const data = {
						phone: this.form.phone,
						phoneCode: this.form.code,
						loginType: "2"
					}
					this.apiEvent(data)
					// 调用后端验证码登录接口
					console.log('手机号验证码登录', this.form.phone, this.form.code);
				}
			},
			// shi na li de tu pian
			// 服用函数
			apiEvent(data) {
				this.$api.request({
					url: this.$api.tologinbycp,
					method: 'POST',
					data
				}).then((res) => {
					console.log(res, '返回');
					let appUser = res.appUser
					let obj = {
						id: appUser.id,
						headImg: appUser.headImg,
						nickName: appUser.nickName,
						phone: appUser.phone,
						realName: appUser.realName,
						nameAuth: appUser.nameAuth
					}
					uni.setStorageSync("user_count_key", res.token)
					uni.setStorageSync("userInfo", obj)
					this.setUserCountKey(res.token)
					this.savecp(data)
					uni.showToast({
						title: '登陆成功'
					})
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/home/<USER>'
						})
					}, 1200)
				})
			},

			// 发送验证码
			sendCode() {
				if (!this.form.phone) {
					return uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
				}
				// 调用后端发送验证码接口
				this.$api.request({
					url: this.$api.getdecode + '?phone=' + this.form.phone,
				}).then((res) => {
					console.log(res, '验证码');
				})
				console.log('发送验证码到', this.form.phone);
				this.countdown = 60;
				const timer = setInterval(() => {
					if (this.countdown > 0) {
						this.countdown--;
					} else {
						clearInterval(timer);
					}
				}, 1000);
			},

			// 微信快捷登录
			wechatLogin() {
				console.log('微信快捷登录');
				// 调用微信登录接口
				uni.showToast({
					title: '微信登录成功',
					icon: 'success'
				});
			},

			// 查看用户协议
			viewTerms() {
				uni.navigateTo({
					url: '/pages/terms/terms'
				});
			},

			// 查看隐私政策
			viewPrivacy() {
				uni.navigateTo({
					url: '/pages/privacy/privacy'
				});
			},
			// 查看注册页
			viewRegister() {
				uni.navigateTo({
					url: '/pages/login/register'
				});
			},
			// 记住密码
			setcp() {
				this.setcpflag = !this.setcpflag
				uni.setStorageSync('setcpflag', this.setcpflag)
				console.log(this.setcpflag);
			},
			// 是否存储密码
			savecp(data) {
				let saveflag = uni.getStorageSync('setcpflag')
				if (saveflag) {
					uni.setStorageSync('savedata', data);
				} else {
					uni.removeStorageSync('savedata')
				}
			},
			// 获取用户记住密码时的操作
			getsavedata() {
				let saveflag = uni.getStorageSync('setcpflag')
				if (saveflag) {
					this.setcpflag = saveflag
					let savedata = uni.getStorageSync('savedata')
					this.form.phone = savedata.phone
					this.form.password = savedata.password
				}
			}
		},
	};
</script>

<style scoped>
	.container {
		height: 100%;
		padding: 20rpx;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.logo {
		width: 100vw;
		height: 20vh;
	}

	.title {
		font-size: 36rpx;
		margin-top: 20rpx;
	}

	.tab-switch {
		display: flex;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.tab-switch button {
		margin: 0 10rpx;
		padding: 10rpx 20rpx;
		border: none;
		background-color: #ccc;
		color: white;
		font-size: 28rpx;
		border-radius: 10rpx;
	}

	.tab-switch .active {
		background-color: #3cb371;
	}

	.form {
		width: 80%;
		display: flex;
		flex-direction: column;
	}

	.input {
		margin-bottom: 20rpx;
		padding: 20rpx;
		border: 1rpx solid #ddd;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	.code-input {
		display: flex;
		justify-content: space-between;
	}

	.send-code-btn {
		margin-left: 10rpx;
		background-color: #3cb371;
		color: white;
		border-radius: 10rpx;
		width: 250rpx;
		height: 80rpx;
		font-size: 30rpx;
		line-height: 80rpx;

	}

	.login-btn {
		width: 100%;
		padding: 20rpx;
		background-color: #3cb371;
		color: white;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	.quick-login {
		margin: 20rpx 0;
		text-align: center;
	}

	.wechat-login-btn {
		margin-top: 10rpx;
		padding: 20rpx;
		background-color: #1aad19;
		color: white;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	.agreement {
		text-align: center;
		margin-top: 30rpx;
		position: fixed;
		bottom: 10%;
		font-size: 26rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.link {
		color: #3cb371;
		margin: 0 5rpx;
	}

	.link_btn {
		display: inline;
		height: 50rpx;
		width: 140rpx;
		display: inline-block;
		background-color: green;
		font-size: 24rpx;
		line-height: 50rpx;
		color: #fff;
		border-radius: 20rpx;
	}
</style>