# 算力匹配算法实现说明

## 概述

本文档描述了 `OrderRecordTask.computingPower()` 方法中实现的算力匹配算法，该算法用于处理今日订单，根据算力进行收付款记录的生成，最大程度地减少收款用户和付款用户的相互交易笔数。

## 算法流程

### 1. 触发条件检查

- 检查是否还存在展示的商品（`is_show = 1` 且 `is_del = 0`）
- 如果存在展示商品，则跳过算法执行
- 如果不存在展示商品，则开始执行算力匹配算法

### 2. 订单分类

根据 `HtOrder.orderType` 字段将今日订单分为三类：

- **收款订单** (`orderType = 0`): 需要收款的订单
- **付款订单** (`orderType = 1`): 需要付款的订单
- **无需付款订单** (`orderType = 3`): 价格相等，无需收付款的订单

### 3. 无需付款订单处理

对于 `orderType = 3` 的订单：

- 直接修改订单状态为 `OrderStatus = NO_PAYMENT_REQUIRED`
- 不生成收付款记录

### 4. 算力匹配算法

#### 4.1 预处理

- 将收款订单和付款订单按金额降序排序
- 创建 `OrderMatchInfo` 对象来跟踪每个订单的剩余匹配金额

#### 4.2 贪心匹配策略

使用贪心算法进行订单匹配：

1. **遍历收款订单**：按金额从大到小处理每个收款订单
2. **寻找最佳匹配**：对于每个收款订单，在付款订单中寻找金额差异最小的订单
3. **执行匹配**：
   - 计算匹配金额 = min(收款订单剩余金额, 付款订单剩余金额)
   - 创建 `HtOrderRecord` 记录
   - 更新双方订单的剩余金额
4. **移除完成订单**：如果订单金额完全匹配，从待匹配列表中移除

#### 4.3 匹配优化

- **优先完全匹配**：优先选择金额完全相等的订单进行匹配
- **最小差异原则**：在无法完全匹配时，选择金额差异最小的订单
- **减少交易笔数**：通过合理的匹配策略，最大程度减少生成的交易记录数量

### 5. 订单记录生成

#### 5.1 匹配成功的订单记录

对于每次成功匹配，生成 `HtOrderRecord` 记录：

- `orderId`: 收款订单 ID
- `userId`: 付款用户 ID
- `recipientId`: 收款用户 ID
- `amount`: 匹配金额
- `type`: 0（待确认状态）

#### 5.2 未匹配订单记录

对于剩余未完全匹配的订单，也需要创建对应的 `HtOrderRecord` 记录：

**未匹配收款订单**：

- `orderId`: 收款订单 ID
- `userId`: null（暂时没有付款人，等待后续匹配）
- `recipientId`: 收款用户 ID
- `amount`: 剩余未匹配金额
- `type`: 0（待确认状态）

**未匹配付款订单**：

- `orderId`: 付款订单 ID
- `userId`: 付款用户 ID
- `recipientId`: 收款用户 ID
- `amount`: 剩余未匹配金额
- `type`: 0（待确认状态）

## 算法特点

### 优势

1. **减少交易笔数**：通过智能匹配算法，最大程度减少用户间的交易次数
2. **公平性**：按金额排序确保大额订单优先处理
3. **灵活性**：支持部分匹配，处理金额不完全相等的情况
4. **容错性**：包含完整的异常处理和日志记录

### 算法复杂度

- **时间复杂度**：O(n²)，其中 n 是订单数量
- **空间复杂度**：O(n)，用于存储订单匹配信息

## 使用示例

```java
// 定时任务调用
@Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
public void executeComputingPower() {
    orderRecordTask.computingPower();
}
```

## 配置说明

### 数据库表结构

- `ht_order`: 订单表，包含 `order_type` 字段
- `ht_order_record`: 订单记录表，存储匹配结果
- `ht_goods`: 商品表，用于判断是否还有展示商品

### 枚举定义

- `OrderStatusEnum.NO_PAYMENT_REQUIRED`: 无需付款状态
- `OrderStatusEnum.PENDING_REVIEW`: 待审核状态

## 注意事项

1. **事务处理**：建议在调用方法时添加事务注解确保数据一致性
2. **并发控制**：如果有多个实例，需要考虑分布式锁避免重复执行
3. **性能监控**：对于大量订单的情况，建议添加性能监控
4. **日志级别**：可以通过调整日志级别来控制输出详细程度

## 扩展建议

1. **算法优化**：可以考虑使用更高效的匹配算法，如匈牙利算法
2. **批量处理**：对于大量数据，可以考虑分批处理
3. **异步处理**：可以将匹配结果异步写入数据库提高性能
4. **监控告警**：添加匹配失败或异常的监控告警机制
