<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8c4763b6-8bf0-4fc1-904e-9fd414f86846" name="更改" comment="修改配置文件">
      <change beforePath="$PROJECT_DIR$/ruoyi-api/src/main/java/com/ruoyi/api/controller/ApiHtGoodsController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-api/src/main/java/com/ruoyi/api/controller/ApiHtGoodsController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-api/src/main/java/com/ruoyi/api/controller/ApiWareHouseController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-api/src/main/java/com/ruoyi/api/controller/ApiWareHouseController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-quartz/src/main/java/com/ruoyi/quartz/task/OrderRecordTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-quartz/src/main/java/com/ruoyi/quartz/task/OrderRecordTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/mapper/HtGoodsMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/mapper/HtGoodsMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/IHtGoodsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/IHtGoodsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtFeeRevenueConfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtFeeRevenueConfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtGoodsServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtGoodsServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtOrderServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtOrderServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtShelvesAuditServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/HtShelvesAuditServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/utils/CreateOrderUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/utils/CreateOrderUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/resources/mapper/system/HtGoodsMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/resources/mapper/system/HtGoodsMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/common/api.js" beforeDir="false" afterPath="$PROJECT_DIR$/../stone-uniapp/common/api.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../stone-uniapp/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/index.2da1efab.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/chunk-vendors.deae5a1f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/index.a43ccad6.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-address-address.4e33eca3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-home-home.b56ff355.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-login-login.8b55877b.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-login-register.26e59d1b.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-login-register_2.51b84fb0.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-myPage.7f4e1338.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-confirm.357b9bee.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-distribution.508a0cf4.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-getpay.3a182cd8.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-getpay~pages-order-orderDetail~pages-order-payDetail.5d2083de.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-merchant.3fd003e8.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-mygetmoney.0306b269.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-myshare.d0ec28ce.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-mystone.31d6bf1b.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-pwd-fixpwd.773bbc1d.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-pwd-fixrealpwd.7b8d9b6f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-pwd.7bb25172.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-realname.5a178444.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-sharecode.84cf41b5.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-signcenter.c2606b54.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-teamorder.3fffbfd0.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-todaymoney.7203f98f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-myPage-sec_page-yestodaybefore.b36c8438.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-order-orderDetail.63600276.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-order-orderDetail~pages-order-payDetail.070f261e.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-order-orderList.9d22ab89.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-order-payDetail.a864366d.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-order-todaygetpay.3c9cfa3d.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-privacy-privacy.130ab20f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-project-detail-project-detail.33719c12.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-seckill-index.ce8835e7.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-seckill-list.a3f2558f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-terms-terms.f3759d32.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-transactiondetails-transactiondetails.53c86188.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../stone-uniapp/unpackage/dist/build/web/static/js/pages-warehouse-warehouse.203afbdb.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="31BYJhk6TWx65OnR1XhOnRhuqQE" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-admin [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/projects/stone/stone-ruoyi&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Maven.ruoyi [install]">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="stone-ruoyi" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ruoyi [clean]" type="MavenRunConfiguration" factoryName="Maven" nameIsGenerated="true">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="clean" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <configuration name="ruoyi [install]" type="MavenRunConfiguration" factoryName="Maven" nameIsGenerated="true">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="install" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <configuration name="ApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.ApiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.WebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Maven.ruoyi [install]" />
      <item itemvalue="Maven.ruoyi [clean]" />
      <item itemvalue="Spring Boot.ApiApplication" />
      <item itemvalue="Spring Boot.WebApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8c4763b6-8bf0-4fc1-904e-9fd414f86846" name="更改" comment="" />
      <created>1754998863397</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754998863397</updated>
      <workItem from="1754998864631" duration="2226000" />
      <workItem from="1755003299870" duration="9996000" />
    </task>
    <task id="LOCAL-00001" summary="修改配置文件">
      <option name="closed" value="true" />
      <created>1755002259311</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755002259311</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="修改配置文件" />
    <option name="LAST_COMMIT_MESSAGE" value="修改配置文件" />
  </component>
</project>