<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品名称" prop="storeName">
        <el-input v-model="queryParams.storeName" placeholder="请输入商品名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!--                  <el-form-item label="是否热卖" prop="isHot">-->
      <!--                    <el-select v-model="queryParams.isHot" placeholder="请选择是否热卖" clearable>-->
      <!--                      <el-option-->
      <!--                          v-for="dict in dict.type.is_not"-->
      <!--                          :key="dict.value"-->
      <!--                          :label="dict.label"-->
      <!--                          :value="dict.value"-->
      <!--                      />-->
      <!--                    </el-select>-->
      <!--                  </el-form-item>-->
      <!--                  <el-form-item label="是否新品" prop="isNew">-->
      <!--                    <el-select v-model="queryParams.isNew" placeholder="请选择是否新品" clearable>-->
      <!--                      <el-option-->
      <!--                          v-for="dict in dict.type.is_not"-->
      <!--                          :key="dict.value"-->
      <!--                          :label="dict.label"-->
      <!--                          :value="dict.value"-->
      <!--                      />-->
      <!--                    </el-select>-->
      <!--                  </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:goods:add']">新增</el-button>
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:goods:remove']">删除</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="goodsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品id" align="center" prop="id" />
      <el-table-column label="商品图片" align="center" prop="image" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.image" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="center" prop="storeName" />
      <el-table-column label="归属人" align="center" prop="realName" />
      <el-table-column label="商品简介" align="center" prop="storeInfo" />
      <el-table-column label="关键字" align="center" prop="keyword" />
      <el-table-column label="商品价格" align="center" prop="price" />
      <el-table-column label="状态" align="center" prop="isShow">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_goods_type" :value="scope.row.isShow" />
        </template>
      </el-table-column>
      <!--              <el-table-column label="是否热卖" align="center" prop="isHot">-->
      <!--                <template slot-scope="scope">-->
      <!--                      <dict-tag :options="dict.type.is_not" :value="scope.row.isHot"/>-->
      <!--                </template>-->
      <!--              </el-table-column>-->
      <el-table-column label="是否精品" align="center" prop="isBest">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_not" :value="scope.row.isBest" />
        </template>
      </el-table-column>
      <el-table-column label="是否新品" align="center" prop="isNew">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_not" :value="scope.row.isNew" />
        </template>
      </el-table-column>
      <el-table-column label="转售价" align="center" prop="resalePrice" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:goods:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:goods:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改商品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品图片" prop="image">
          <image-upload :limit="1" v-model="form.image" />
        </el-form-item>
        <el-form-item label="轮播图" prop="sliderImage">
          <image-upload v-model="form.sliderImage" />
        </el-form-item>
        <el-form-item label="商品名称" prop="storeName">
          <el-input v-model="form.storeName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品简介">
          <editor v-model="form.storeInfo" :min-height="192" />
        </el-form-item>
        <el-form-item label="关键字" prop="keyword">
          <el-input v-model="form.keyword" placeholder="请输入关键字" />
        </el-form-item>
        <el-form-item label="商品价格" prop="price">
          <el-input-number :precision="2" :min="0" v-model="form.price" placeholder="请输入商品价格" />
        </el-form-item>
        <el-form-item label="状态" prop="isShow">
          <el-select v-model="form.isShow" placeholder="请选择状态">
            <el-option v-for="dict in dict.type.sys_goods_type" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否精品" prop="isBest">
          <el-select v-model="form.isBest" placeholder="请选择是否精品">
            <el-option v-for="dict in dict.type.is_not" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否新品" prop="isNew">
          <el-select v-model="form.isNew" placeholder="请选择是否新品">
            <el-option v-for="dict in dict.type.is_not" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归属人" prop="userId">
          <el-select v-model="form.userId" placeholder="请选择归属人" clearable>
            <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listGoods, getGoods, delGoods, addGoods, updateGoods } from "@/api/system/goods";
import { selectUserList } from "@/api/system/appuser";

export default {
  name: "Goods",
  dicts: ['sys_goods_type', 'is_not'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品表格数据
      goodsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        storeName: null,
        isHot: null,
        isNew: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        storeName: [
          { required: true, message: "商品名称不能为空", trigger: "blur" }
        ],
        cateId: [
          { required: true, message: "分类id不能为空", trigger: "blur" }
        ],
      },
      // 用户选项
      userOptions: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商品列表 */
    getList() {
      this.loading = true;
      listGoods(this.queryParams).then(response => {
        this.goodsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        image: null,
        sliderImage: null,
        storeName: null,
        storeInfo: null,
        keyword: null,
        cateId: null,
        price: null,
        sort: null,
        sales: null,
        isShow: null,
        isHot: null,
        isBest: null,
        isNew: null,
        isDel: null,
        resalePrice: null,
        activityId: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品";
    },
    /** 查询用户下拉列表 */
    getUserOptions() {
      selectUserList().then(response => {
        if (response.code === 200) {
          this.userOptions = response.data.map(item => ({
            value: item.id,
            label: item.realName
          }));
        }
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGoods(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品";
        this.getUserOptions();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateGoods(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.userId = 0;
            addGoods(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除商品编号为"' + ids + '"的数据项？').then(function () {
        return delGoods(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    }
  }
};
</script>
