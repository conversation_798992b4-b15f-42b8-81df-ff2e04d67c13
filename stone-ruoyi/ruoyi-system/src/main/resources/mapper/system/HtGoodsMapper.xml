<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HtGoodsMapper">

    <resultMap type="HtGoods" id="HtGoodsResult">
            <result property="id" column="id"/>
            <result property="userId" column="user_id"/>
            <result property="image" column="image"/>
            <result property="sliderImage" column="slider_image"/>
            <result property="storeName" column="store_name"/>
            <result property="storeInfo" column="store_info"/>
            <result property="keyword" column="keyword"/>
            <result property="cateId" column="cate_id"/>
            <result property="price" column="price"/>
            <result property="sort" column="sort"/>
            <result property="sales" column="sales"/>
            <result property="isShow" column="is_show"/>
            <result property="isHot" column="is_hot"/>
            <result property="isBest" column="is_best"/>
            <result property="isNew" column="is_new"/>
            <result property="isDel" column="is_del"/>
            <result property="resalePrice" column="resale_price"/>
            <result property="activityId" column="activity_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectHtGoodsVo">
        select id, user_id, image, slider_image, store_name, store_info, keyword, cate_id, price, sort, sales, is_show, is_hot, is_best, is_new, is_del, resale_price, activity_id, create_time, update_time
        from ht_goods
    </sql>

    <select id="selectHtGoodsList" parameterType="HtGoods" resultMap="HtGoodsResult">
        <include refid="selectHtGoodsVo"/>
        <where>
            <if test="storeName != null  and storeName != ''">
                and store_name like concat('%', #{storeName}, '%')
            </if>
            <if test="isBest != null ">
                and is_best = #{isBest}
            </if>
            <if test="isHot != null ">
                and is_hot = #{isHot}
            </if>
            <if test="isNew != null ">
                and is_new = #{isNew}
            </if>
            <if test="isShow != null ">
                and is_show = #{isShow}
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            <if test="userId != null">
                and user_id != #{userId}
            </if>
            and is_del = 0 and id != 0
        </where>

    </select>

    <select id="selectHtGoodsById" parameterType="Long"
            resultMap="HtGoodsResult">
            <include refid="selectHtGoodsVo"/>
            where id = #{id}
    </select>

    <insert id="insertHtGoods" parameterType="HtGoods" useGeneratedKeys="true"
            keyProperty="id">
        insert into ht_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,
                    </if>
                    <if test="image != null">image,
                    </if>
                    <if test="sliderImage != null">slider_image,
                    </if>
                    <if test="storeName != null and storeName != ''">store_name,
                    </if>
                    <if test="storeInfo != null">store_info,
                    </if>
                    <if test="keyword != null">keyword,
                    </if>
                    <if test="cateId != null and cateId != ''">cate_id,
                    </if>
                    <if test="price != null">price,
                    </if>
                    <if test="sort != null">sort,
                    </if>
                    <if test="sales != null">sales,
                    </if>
                    <if test="isShow != null">is_show,
                    </if>
                    <if test="isHot != null">is_hot,
                    </if>
                    <if test="isBest != null">is_best,
                    </if>
                    <if test="isNew != null">is_new,
                    </if>
                    <if test="isDel != null">is_del,
                    </if>
                    <if test="resalePrice != null">resale_price,
                    </if>
                    <if test="activityId != null">activity_id,
                    </if>
                    <if test="createTime != null">create_time,
                    </if>
                    <if test="updateTime != null">update_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},
                    </if>
                    <if test="image != null">#{image},
                    </if>
                    <if test="sliderImage != null">#{sliderImage},
                    </if>
                    <if test="storeName != null and storeName != ''">#{storeName},
                    </if>
                    <if test="storeInfo != null">#{storeInfo},
                    </if>
                    <if test="keyword != null">#{keyword},
                    </if>
                    <if test="cateId != null and cateId != ''">#{cateId},
                    </if>
                    <if test="price != null">#{price},
                    </if>
                    <if test="sort != null">#{sort},
                    </if>
                    <if test="sales != null">#{sales},
                    </if>
                    <if test="isShow != null">#{isShow},
                    </if>
                    <if test="isHot != null">#{isHot},
                    </if>
                    <if test="isBest != null">#{isBest},
                    </if>
                    <if test="isNew != null">#{isNew},
                    </if>
                    <if test="isDel != null">#{isDel},
                    </if>
                    <if test="resalePrice != null">#{resalePrice},
                    </if>
                    <if test="activityId != null">#{activityId},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
                    <if test="updateTime != null">#{updateTime},
                    </if>
        </trim>
    </insert>

    <update id="updateHtGoods" parameterType="HtGoods">
        update ht_goods
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id =
                        #{userId},
                    </if>
                    <if test="image != null">image =
                        #{image},
                    </if>
                    <if test="sliderImage != null">slider_image =
                        #{sliderImage},
                    </if>
                    <if test="storeName != null and storeName != ''">store_name =
                        #{storeName},
                    </if>
                    <if test="storeInfo != null">store_info =
                        #{storeInfo},
                    </if>
                    <if test="keyword != null">keyword =
                        #{keyword},
                    </if>
                    <if test="cateId != null and cateId != ''">cate_id =
                        #{cateId},
                    </if>
                    <if test="price != null">price =
                        #{price},
                    </if>
                    <if test="sort != null">sort =
                        #{sort},
                    </if>
                    <if test="sales != null">sales =
                        #{sales},
                    </if>
                    <if test="isShow != null">is_show =
                        #{isShow},
                    </if>
                    <if test="isHot != null">is_hot =
                        #{isHot},
                    </if>
                    <if test="isBest != null">is_best =
                        #{isBest},
                    </if>
                    <if test="isNew != null">is_new =
                        #{isNew},
                    </if>
                    <if test="isDel != null">is_del =
                        #{isDel},
                    </if>
                    <if test="resalePrice != null">resale_price =
                        #{resalePrice},
                    </if>
                    <if test="activityId != null">activity_id =
                        #{activityId},
                    </if>
                    <if test="createTime != null">create_time =
                        #{createTime},
                    </if>
                    <if test="updateTime != null">update_time =
                        #{updateTime},
                    </if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateHtGoodsByIsDel">
        update ht_goods
        <trim prefix="SET" suffixOverrides=",">
        <if test="isDel != null">is_del =
            #{isDel},
        </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHtGoodsById" parameterType="Long">
        delete
        from ht_goods where id = #{id}
    </delete>

    <delete id="deleteHtGoodsByIds" parameterType="String">
        delete from ht_goods where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="splitGoods"  resultMap="HtGoodsResult">
        select * from ht_goods where price >= 30000 and is_del = '0' and is_show = '1';
    </select>

    <select id="selectGoodsByUserIdNotShow"  resultType="Long">
        select * from ht_goods where user_id =#{userId} and is_show=0 and is_del=0 and id not in (select goods_id from ht_order where  DATE(created_at) = CURDATE());
    </select>

    <select id="selectMyHtGoodsList" resultType="com.ruoyi.system.domain.HtGoods">
        <include refid="selectHtGoodsVo"/>
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            and is_del = 0 and id != 0
        </where>
    </select>
    <select id="selectSystemHtGoodsList" resultType="com.ruoyi.system.domain.HtGoods">
        select hg.*,au.real_name
            from ht_goods hg
        left join app_user au
        on au.id = hg.user_id
        <where>
        <if test="storeName != null  and storeName != ''">
            and hg.store_name like concat('%', #{storeName}, '%')
        </if>
            and hg.is_del = 0 and hg.id != 0
        </where>
    </select>
</mapper>