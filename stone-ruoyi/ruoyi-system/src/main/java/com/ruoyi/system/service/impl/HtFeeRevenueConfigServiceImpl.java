package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.concurrent.TimeUnit;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils; //需要修改成自己的包名
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.HtFeeRevenueConfigMapper;
import com.ruoyi.system.domain.HtFeeRevenueConfig;
import com.ruoyi.system.service.IHtFeeRevenueConfigService;


/**
 * 上架费 收益配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Service
public class HtFeeRevenueConfigServiceImpl extends ServiceImpl<HtFeeRevenueConfigMapper,HtFeeRevenueConfig> implements IHtFeeRevenueConfigService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private HtFeeRevenueConfigMapper htFeeRevenueConfigMapper;

    /**
     * 查询上架费 收益配置
     *
     * @param id 上架费 收益配置主键
     * @return 上架费 收益配置
     */
    @Override
    public HtFeeRevenueConfig selectHtFeeRevenueConfigById(Long id) {
        return htFeeRevenueConfigMapper.selectHtFeeRevenueConfigById(id);
    }

    /**
     * 查询上架费 收益配置列表
     *
     * @param htFeeRevenueConfig 上架费 收益配置
     * @return 上架费 收益配置
     */
    @Override
    public List<HtFeeRevenueConfig> selectHtFeeRevenueConfigList(HtFeeRevenueConfig htFeeRevenueConfig) {
        return htFeeRevenueConfigMapper.selectHtFeeRevenueConfigList(htFeeRevenueConfig);
    }

    /**
     * 新增上架费 收益配置
     *
     * @param htFeeRevenueConfig 上架费 收益配置
     * @return 结果
     */
    @Override
    public int insertHtFeeRevenueConfig(HtFeeRevenueConfig htFeeRevenueConfig) {
                htFeeRevenueConfig.setCreateTime(DateUtils.getNowDate());
            return htFeeRevenueConfigMapper.insertHtFeeRevenueConfig(htFeeRevenueConfig);
    }

    /**
     * 修改上架费 收益配置
     *
     * @param htFeeRevenueConfig 上架费 收益配置
     * @return 结果
     */
    @Override
    public int updateHtFeeRevenueConfig(HtFeeRevenueConfig htFeeRevenueConfig) {
        htFeeRevenueConfig.setUpdateTime(DateUtils.getNowDate());
        String configKey = "SNAPPING_TIME_CONFIG";
        redisCache.setCacheObject(configKey, htFeeRevenueConfig, 24, TimeUnit.HOURS);
        return htFeeRevenueConfigMapper.updateHtFeeRevenueConfig(htFeeRevenueConfig);
    }

    /**
     * 批量删除上架费 收益配置
     *
     * @param ids 需要删除的上架费 收益配置主键
     * @return 结果
     */
    @Override
    public int deleteHtFeeRevenueConfigByIds(Long[] ids) {
        return htFeeRevenueConfigMapper.deleteHtFeeRevenueConfigByIds(ids);
    }

    /**
     * 删除上架费 收益配置信息
     *
     * @param id 上架费 收益配置主键
     * @return 结果
     */
    @Override
    public int deleteHtFeeRevenueConfigById(Long id) {
        return htFeeRevenueConfigMapper.deleteHtFeeRevenueConfigById(id);
    }
}

