package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DateUtils; //需要修改成自己的包名
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.vo.HtShelvesAuditDto;
import com.ruoyi.system.domain.vo.HtShelvesAuditVo;
import com.ruoyi.system.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.IHtShelvesAuditService;
import org.springframework.transaction.annotation.Transactional;


/**
 * 石头委托审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class HtShelvesAuditServiceImpl extends ServiceImpl<HtShelvesAuditMapper, HtShelvesAudit> implements IHtShelvesAuditService {
    @Autowired
    private HtShelvesAuditMapper htShelvesAuditMapper;

    @Autowired
    private AppUserMapper appUserMapper;

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtGoodsMapper htGoodsMapper;
    @Autowired
    private HtUserSharesMapper htUserSharesMapper;

    @Autowired
    private HtIncomeMapper htIncomeMapper;

    @Autowired
    private HtBalanceMapper htBalanceMapper;
    @Autowired
    private ComputingConfigUtils computingConfigUtils;

    /**
     * 查询石头委托审核
     *
     * @param id 石头委托审核主键
     * @return 石头委托审核
     */
    @Override
    public HtShelvesAudit selectHtShelvesAuditById(Long id) {
        return htShelvesAuditMapper.selectHtShelvesAuditById(id);
    }

    /**
     * 查询石头委托审核列表
     *
     * @param htShelvesAudit 石头委托审核
     * @return 石头委托审核
     */
    @Override
    public List<HtShelvesAudit> selectHtShelvesAuditList(HtShelvesAudit htShelvesAudit) {
        return htShelvesAuditMapper.selectHtShelvesAuditList(htShelvesAudit);
    }

    /**
     * 新增石头委托审核
     *
     * @param htShelvesAudit 石头委托审核
     * @return 结果
     */
    @Override
    public int insertHtShelvesAudit(HtShelvesAudit htShelvesAudit) {
        htShelvesAudit.setCreateTime(DateUtils.getNowDate());
        return htShelvesAuditMapper.insertHtShelvesAudit(htShelvesAudit);
    }

    /**
     * 修改石头委托审核
     *
     * @param htShelvesAudit 石头委托审核
     * @return 结果
     */
    @Override
    public int updateHtShelvesAudit(HtShelvesAudit htShelvesAudit) {
        htShelvesAudit.setUpdateTime(DateUtils.getNowDate());
        return htShelvesAuditMapper.updateHtShelvesAudit(htShelvesAudit);
    }

    /**
     * 批量删除石头委托审核
     *
     * @param ids 需要删除的石头委托审核主键
     * @return 结果
     */
    @Override
    public int deleteHtShelvesAuditByIds(Long[] ids) {
        return htShelvesAuditMapper.deleteHtShelvesAuditByIds(ids);
    }

    /**
     * 删除石头委托审核信息
     *
     * @param id 石头委托审核主键
     * @return 结果
     */
    @Override
    public int deleteHtShelvesAuditById(Long id) {
        return htShelvesAuditMapper.deleteHtShelvesAuditById(id);
    }

    @Override
    @Transactional
    public int submit(HtShelvesAuditDto htShelvesAudit) {
        if (baseMapper.selectOne(Wrappers.<HtShelvesAudit>query().eq("user_id",htShelvesAudit.getUserId()).like("DATE_FORMAT(create_time, '%Y-%m-%d')", DateUtils.getDate()).orderByDesc("create_time").last(" limit 1"))!=null){
            AjaxResult.error("已提交该审核");
        }
        AppUser user = appUserMapper.selectById(htShelvesAudit.getUserId());
        HtOrder order = new HtOrder();
        order.setId(htShelvesAudit.getOrderId().longValue());
        order.setOrderStatus(5L);

        BigDecimal userBalance = user.getBalance();
        //余额小于手续费
        if (htShelvesAudit.getType() == 1) {
            //修改订单状态
            htOrderMapper.updateHtOrder(order);
            HtShelvesAudit audit = getHtShelvesAudit(htShelvesAudit);
            audit.setUploadImg(htShelvesAudit.getUploadImg());
            audit.setPrice(htShelvesAudit.getPrice().subtract(userBalance));
            audit.setBalance(userBalance);
            audit.setType(1);
            audit.setCreateTime(new Date());
            audit.setUpdateTime(new Date());
            baseMapper.insert(audit);

            //插入临时余额
            HtBalance htBalance = new HtBalance();
            htBalance.setBalance(userBalance);
            htBalance.setUserId(user.getId().intValue());
            htBalance.setCreateTime(new Date());
            htBalance.setAuditId(audit.getId().intValue());
            return htBalanceMapper.insert(htBalance);

            //修改用户余额
//            user.setBalance(new BigDecimal(0));
//            return
//            appUserMapper.updateAppUser(user);

            //余额大于手续费
        } else if (htShelvesAudit.getType() == 2) {
            //修改订单状态
            htOrderMapper.updateHtOrder(order);
            HtShelvesAudit audit = getHtShelvesAudit(htShelvesAudit);
            audit.setPrice(htShelvesAudit.getPrice());
            audit.setBalance(userBalance);
            audit.setCreateTime(new Date());
            audit.setUpdateTime(new Date());
            audit.setType(2);
            baseMapper.insert(audit);
            //插入临时余额
            HtBalance htBalance = new HtBalance();
            htBalance.setBalance(htShelvesAudit.getPrice());
            htBalance.setUserId(user.getId().intValue());
            htBalance.setCreateTime(new Date());
            htBalance.setAuditId(audit.getId().intValue());

            return htBalanceMapper.insert(htBalance);


            //修改用户余额
//            user.setBalance(userBalance.subtract(htShelvesAudit.getPrice()));
//            return appUserMapper.updateAppUser(user);
        } else {
            //修改订单状态
            htOrderMapper.updateHtOrder(order);
            //没有走余额
            HtShelvesAudit auto = getHtShelvesAudit(htShelvesAudit);
            auto.setType(0);
            auto.setPrice(htShelvesAudit.getPrice());
            auto.setBalance(userBalance);
            auto.setCreateTime(new Date());
            auto.setUpdateTime(new Date());
            auto.setUploadImg(htShelvesAudit.getUploadImg());
            return baseMapper.insert(auto);
        }


    }

    @Override
    public List<HtShelvesAuditVo> selectAdminHtShelvesAuditList(HtShelvesAudit htShelvesAudit) {
        return htShelvesAuditMapper.selectAdminHtShelvesAuditList(htShelvesAudit);
    }
//    public HtGoods splitGoods(HtGoods htGoods1) {
//
//        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
//        BigDecimal decimal = new BigDecimal("25000");
//        List<HtGoods> list = htGoodsMapper.splitGoods1();
//        HtGoods tmp = new HtGoods();
//        HtGoods tmp2 = new HtGoods();
//        for (HtGoods htGoods : list) {
//            BeanUtils.copyBeanProp(tmp,htGoods);
//            BeanUtils.copyBeanProp(tmp2,htGoods);
//            tmp.setPrice(decimal);
//            tmp2.setPrice(htGoods1.get().subtract(decimal));
//
//            tmp.setResalePrice(decimal.multiply(Price_increase_rate).add(decimal));
//            tmp2.setResalePrice(htGoods.getResalePrice().subtract(decimal.multiply(Price_increase_rate).add(decimal)));
//
//            htGoodsMapper.insertHtGoods(tmp);
//            htGoodsMapper.insertHtGoods(tmp2);
//            //进行逻辑删除
//            htGoods.setIsDel(1);
//            htGoodsMapper.updateHtGoodsByIsDel(htGoods);
//        }
//    }
    //审核
    @Override
    @Transactional
    public int audit(HtShelvesAudit htShelvesAudit) {
        //通过id查询出石头审核表对象
        HtShelvesAudit audit = baseMapper.selectHtShelvesAuditById(htShelvesAudit.getId());
        HtUserShares userShares = htUserSharesMapper.selectOne(Wrappers.<HtUserShares>query().eq("user_id", audit.getUserId()));
        HtGoods htGoods = htGoodsMapper.selectOne(Wrappers.<HtGoods>query().eq("id", audit.getGoodsId()));
        //得到最近需要扣除的余额
        HtBalance htBalance = htBalanceMapper.selectOne(Wrappers.<HtBalance>query()
                .eq("audit_id", audit.getId()));
        if (htBalance!=null) {
            AppUser user1 = appUserMapper.selectById(audit.getUserId());
            user1.setBalance(user1.getBalance().subtract(htBalance.getBalance()));
            appUserMapper.updateAppUser(user1);
        }

        //团队收益
        if (userShares != null) {
            AppUser user = appUserMapper.selectById(userShares.getMyId());
            BigDecimal multiply = htGoods.getPrice().multiply(new BigDecimal("0.004"));
            BigDecimal balance = user.getBalance().add(multiply);
            user.setBalance(balance);
            appUserMapper.updateAppUser(user);
        }
        //个人收益
        HtIncome htIncome = getHtIncome(htGoods);
        BigDecimal price = htGoods.getPrice();
        BigDecimal resalePrice = htGoods.getResalePrice();
        //拿到算力配置中的上架费率和收益率

        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();


        //新增收益记录
        htIncome.setMyRevenue(htFeeRevenueConfig.getRevenueValue().multiply(price));
        htIncomeMapper.insertHtIncome(htIncome);
        //修改订单的状态
        HtOrder order = htOrderMapper.selectHtOrderById(audit.getOrderId());
        order.setOrderStatus(6L);
        int i1 = htOrderMapper.updateById(order);
        audit.setStatus(1L);
        audit.setUpdateTime(new Date());
        //修改石头委托审核状态
        baseMapper.updateById(audit);
        //修改商品 把商品改为上架
        htGoods.setIsHot(1);
        htGoods.setPrice(resalePrice);
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(resalePrice.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        int i = htGoodsMapper.updateHtGoods(htGoods);
        //把用户余额进行修改
        AppUser user = appUserMapper.selectById(audit.getUserId());
        if (audit.getBalance().compareTo(audit.getPrice()) > 0) {
//            user.setBalance(audit.getBalance().subtract(htShelvesAudit.getPrice()));
//            int i2 = appUserMapper.updateAppUser(user);
            return i>0 &&i1>0 ? 1:0;
        } else {
//            user.setBalance(new BigDecimal(0));
//            int i2 = appUserMapper.updateAppUser(user);
            return i>0 &&i1>0 ? 1:0;
        }
    }
    //封装收益
    private HtIncome getHtIncome(HtGoods goods) {
        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        HtIncome htIncome = new HtIncome();
        htIncome.setGoodsId(goods.getId().intValue());
        htIncome.setUserId(goods.getUserId());
        htIncome.setGoodsImg(goods.getImage());
        htIncome.setResalePrice(goods.getPrice().multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htIncome.setCreateTime(new Date());
        htIncome.setPrice(goods.getPrice());
        htIncome.setGoodsName(goods.getStoreName());
//        htIncome.setMyRevenue(goods.getResalePrice().subtract(goods.getPrice()));
        return htIncome;
    }
    //驳回
    @Override
    @Transactional
    public int overrule(HtShelvesAudit htShelvesAudit) {
        return 0;
    }

    /**
     * 封装实体
     *
     * @return
     */
    public HtShelvesAudit getHtShelvesAudit(HtShelvesAuditDto htShelvesAudit) {
        HtShelvesAudit audit = new HtShelvesAudit();
        audit.setOrderId(htShelvesAudit.getOrderId().longValue());
        audit.setGoodsId(htShelvesAudit.getGoodsId().longValue());
        audit.setUserId(htShelvesAudit.getUserId().longValue());
        audit.setBalance(new BigDecimal(0));
        audit.setStatus(0L);

        return audit;
    }
}

