package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.mapper.HtOrderMapper;
import com.ruoyi.system.mapper.HtOrderRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-08-12
 * @description: 异步处理所有抢购完成的订单
 */
@Slf4j
@Component("OrderRecordTask")
public class OrderRecordTask {

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;

    //今日订单，根据算力进行收付款记录的生成
    public void computingPower() {
        if(shouldTriggerAlgorithm()){
            executeAlgorithmAsync();
        }
    }

    /**
     * 检查是否需要触发算法
     */
    private boolean shouldTriggerAlgorithm() {
        List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("is_show", 1)
                .eq("is_del", 0));
        return goodsList.isEmpty();
    }

    /**
     * 执行算法
     */
    private void executeAlgorithmAsync() {
        try {
            log.info("开始执行算力匹配算法");

            // 查询今天所有待处理的订单
            List<HtOrder> orderList = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                    .like("created_at", DateUtils.getDate())
                    .eq("order_status", OrderStatusEnum.PENDING_REVIEW.getCode()));

            if (orderList.isEmpty()) {
                log.info("今日无待处理订单");
                return;
            }

            log.info("今日待处理订单数量: {}", orderList.size());

            // 分类订单：收款订单、付款订单、无需付款订单
            List<HtOrder> receiveOrders = new ArrayList<>();  // orderType = 0 收款订单
            List<HtOrder> payOrders = new ArrayList<>();      // orderType = 1 付款订单
            List<HtOrder> noPaymentOrders = new ArrayList<>(); // orderType = 3 无需付款订单

            for (HtOrder order : orderList) {
                if (order.getOrderType() == null) {
                    log.warn("订单 {} 的 orderType 为空，跳过处理", order.getId());
                    continue;
                }

                switch (order.getOrderType()) {
                    case 0:
                        receiveOrders.add(order);
                        order.setOrderStatus(OrderStatusEnum.PAID.getCode());
                        order.setUpdatedAt(new Date());
                        htOrderMapper.updateById(order);
                        break;
                    case 1:
                        payOrders.add(order);
                        order.setOrderStatus(OrderStatusEnum.SPECIAL_STATUS.getCode());
                        order.setUpdatedAt(new Date());
                        htOrderMapper.updateById(order);
                        break;
                    case 3:
                        noPaymentOrders.add(order);
                        order.setOrderStatus(OrderStatusEnum.NO_PAYMENT_REQUIRED.getCode());
                        order.setUpdatedAt(new Date());
                        htOrderMapper.updateById(order);
                        break;
                    default:
                        log.warn("订单 {} 的 orderType {} 不在预期范围内", order.getId(), order.getOrderType());
                        break;
                }
            }

            log.info("订单分类完成 - 收款订单: {}, 付款订单: {}, 无需付款订单: {}",
                    receiveOrders.size(), payOrders.size(), noPaymentOrders.size());

            // 执行算力匹配算法
            performOrderMatching(receiveOrders, payOrders);

            log.info("算力匹配算法执行完成");

        } catch (Exception e) {
            log.error("执行算法异常", e);
        }
    }


    /**
     * 执行订单匹配算法 - 最大程度减少收款用户和付款用户的相互交易笔数
     */
    private void performOrderMatching(List<HtOrder> receiveOrders, List<HtOrder> payOrders) {
        if (receiveOrders.isEmpty() && payOrders.isEmpty()) {
            log.info("无收款或付款订单需要匹配");
            return;
        }

        log.info("开始执行订单匹配算法，收款订单: {}, 付款订单: {}", receiveOrders.size(), payOrders.size());

        // 按金额排序，便于匹配
        receiveOrders.sort((o1, o2) -> o2.getAmount().compareTo(o1.getAmount())); // 收款订单按金额降序
        payOrders.sort((o1, o2) -> o2.getAmount().compareTo(o1.getAmount()));    // 付款订单按金额降序

        // 创建订单副本用于匹配处理
        List<OrderMatchInfo> receiveList = receiveOrders.stream()
                .map(order -> new OrderMatchInfo(order, order.getAmount()))
                .collect(Collectors.toList());

        List<OrderMatchInfo> payList = payOrders.stream()
                .map(order -> new OrderMatchInfo(order, order.getAmount()))
                .collect(Collectors.toList());

        // 执行匹配算法
        executeMatching(receiveList, payList);

        log.info("订单匹配算法执行完成");
    }

    /**
     * 执行具体的匹配逻辑
     */
    private void executeMatching(List<OrderMatchInfo> receiveList, List<OrderMatchInfo> payList) {
        int matchCount = 0;

        // 使用贪心算法进行匹配，优先匹配金额相等或接近的订单
        Iterator<OrderMatchInfo> receiveIterator = receiveList.iterator();

        while (receiveIterator.hasNext()) {
            OrderMatchInfo receiveInfo = receiveIterator.next();

            if (receiveInfo.remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                receiveIterator.remove();
                continue;
            }

            // 寻找最佳匹配的付款订单
            OrderMatchInfo bestMatch = findBestMatch(receiveInfo, payList);

            if (bestMatch != null) {
                // 执行匹配
                BigDecimal matchAmount = receiveInfo.remainingAmount.min(bestMatch.remainingAmount);

                // 创建订单记录
                createMatchedOrderRecord(receiveInfo.order, bestMatch.order, matchAmount);

                // 更新剩余金额
                receiveInfo.remainingAmount = receiveInfo.remainingAmount.subtract(matchAmount);
                bestMatch.remainingAmount = bestMatch.remainingAmount.subtract(matchAmount);

                matchCount++;

                // 如果付款订单已完全匹配，从列表中移除
                if (bestMatch.remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    payList.remove(bestMatch);
                }

                // 如果收款订单已完全匹配，从迭代器中移除
                if (receiveInfo.remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    receiveIterator.remove();
                }

                log.debug("成功匹配订单，收款订单: {}, 付款订单: {}, 匹配金额: {}",
                        receiveInfo.order.getId(), bestMatch.order.getId(), matchAmount);
            }
        }

        // 处理未完全匹配的订单
        processUnmatchedOrders(receiveList, payList);

        log.info("匹配完成，共生成 {} 笔交易记录", matchCount);
    }

    /**
     * 寻找最佳匹配的付款订单
     */
    private OrderMatchInfo findBestMatch(OrderMatchInfo receiveInfo, List<OrderMatchInfo> payList) {
        if (payList.isEmpty()) {
            return null;
        }

        OrderMatchInfo bestMatch = null;
        BigDecimal minDifference = null;

        for (OrderMatchInfo payInfo : payList) {
            if (payInfo.remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算金额差异
            BigDecimal difference = receiveInfo.remainingAmount.subtract(payInfo.remainingAmount).abs();

            if (bestMatch == null || difference.compareTo(minDifference) < 0) {
                bestMatch = payInfo;
                minDifference = difference;
            }

            // 如果找到完全匹配的金额，直接返回
            if (difference.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
        }

        return bestMatch;
    }

    /**
     * 创建匹配的订单记录
     */
    private void createMatchedOrderRecord(HtOrder receiveOrder, HtOrder payOrder, BigDecimal amount) {
        try {
            // 创建收款记录
            HtOrderRecord receiveRecord = new HtOrderRecord();
            receiveRecord.setOrderId(receiveOrder.getId().intValue());
            receiveRecord.setUserId(payOrder.getUserId().intValue()); // 付款人
            receiveRecord.setRecipientId(receiveOrder.getRecipientId()); // 收款人
            receiveRecord.setRecipientName(receiveOrder.getRecipientName());
            receiveRecord.setRecipientPhone(receiveOrder.getRecipientPhone());
            receiveRecord.setAmount(amount);
            receiveRecord.setCreateTime(new Date());
            receiveRecord.setType(0); // 待确认

            htOrderRecordMapper.insert(receiveRecord);

            log.debug("创建订单记录成功，收款订单: {}, 付款订单: {}, 金额: {}",
                    receiveOrder.getId(), payOrder.getId(), amount);

        } catch (Exception e) {
            log.error("创建订单记录失败，收款订单: {}, 付款订单: {}, 金额: {}",
                    receiveOrder.getId(), payOrder.getId(), amount, e);
        }
    }

    /**
     * 处理未完全匹配的订单
     */
    private void processUnmatchedOrders(List<OrderMatchInfo> receiveList, List<OrderMatchInfo> payList) {
        int unmatchedCount = 0;

        // 处理剩余的收款订单
        for (OrderMatchInfo receiveInfo : receiveList) {
            if (receiveInfo.remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("收款订单 {} 未完全匹配，剩余金额: {}",
                        receiveInfo.order.getId(), receiveInfo.remainingAmount);

                // 为未匹配的收款订单创建记录
                createUnmatchedReceiveRecord(receiveInfo.order, receiveInfo.remainingAmount);
                unmatchedCount++;
            }
        }

        // 处理剩余的付款订单
        for (OrderMatchInfo payInfo : payList) {
            if (payInfo.remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("付款订单 {} 未完全匹配，剩余金额: {}",
                        payInfo.order.getId(), payInfo.remainingAmount);

                // 为未匹配的付款订单创建记录
                createUnmatchedPayRecord(payInfo.order, payInfo.remainingAmount);
                unmatchedCount++;
            }
        }

        if (unmatchedCount > 0) {
            log.info("共处理 {} 个未完全匹配的订单，已创建对应的订单记录", unmatchedCount);
        }
    }

    /**
     * 为未匹配的收款订单创建记录
     */
    private void createUnmatchedReceiveRecord(HtOrder receiveOrder, BigDecimal amount) {
        try {
            HtOrderRecord orderRecord = new HtOrderRecord();
            orderRecord.setOrderId(receiveOrder.getId().intValue());
            orderRecord.setUserId(null); // 收款订单暂时没有付款人，等待后续匹配
            orderRecord.setRecipientId(receiveOrder.getRecipientId()); // 收款人
            orderRecord.setRecipientName(receiveOrder.getRecipientName());
            orderRecord.setRecipientPhone(receiveOrder.getRecipientPhone());
            orderRecord.setAmount(amount);
            orderRecord.setCreateTime(new Date());
            orderRecord.setType(0); // 待确认状态

            htOrderRecordMapper.insert(orderRecord);

            log.debug("为未匹配收款订单 {} 创建记录成功，金额: {}", receiveOrder.getId(), amount);

        } catch (Exception e) {
            log.error("为未匹配收款订单 {} 创建记录失败，金额: {}", receiveOrder.getId(), amount, e);
        }
    }

    /**
     * 为未匹配的付款订单创建记录
     */
    private void createUnmatchedPayRecord(HtOrder payOrder, BigDecimal amount) {
        try {
            HtOrderRecord orderRecord = new HtOrderRecord();
            orderRecord.setOrderId(payOrder.getId().intValue());
            orderRecord.setUserId(payOrder.getUserId().intValue()); // 付款人
            orderRecord.setRecipientId(payOrder.getRecipientId()); // 收款人
            orderRecord.setRecipientName(payOrder.getRecipientName());
            orderRecord.setRecipientPhone(payOrder.getRecipientPhone());
            orderRecord.setAmount(amount);
            orderRecord.setCreateTime(new Date());
            orderRecord.setType(0); // 待确认状态

            htOrderRecordMapper.insert(orderRecord);

            log.debug("为未匹配付款订单 {} 创建记录成功，金额: {}", payOrder.getId(), amount);

        } catch (Exception e) {
            log.error("为未匹配付款订单 {} 创建记录失败，金额: {}", payOrder.getId(), amount, e);
        }
    }

    /**
     * 订单匹配信息内部类
     */
    private static class OrderMatchInfo {
        HtOrder order;
        BigDecimal remainingAmount;

        public OrderMatchInfo(HtOrder order, BigDecimal amount) {
            this.order = order;
            this.remainingAmount = amount;
        }
    }
}