package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.mapper.HtGoodsMapper;
import com.ruoyi.system.mapper.HtOrderMapper;
import com.ruoyi.system.mapper.HtOrderRecordMapper;
import com.ruoyi.system.service.HtOrderRecordService;
import com.ruoyi.system.utils.CreateOrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-08-12
 * @description: 异步处理所有抢购完成的订单
 */
@Slf4j
@Component("OrderRecordTask")
public class OrderRecordTask {

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    @Autowired
    private HtOrderMapper htOrderMapper;


    @Autowired
    private CreateOrderUtils createOrderUtils;

    //今日订单，根据算力进行收付款记录的生成
    public void computingPower() {
        if(shouldTriggerAlgorithm()){
            executeAlgorithmAsync();
        }
    }

    /**
     * 检查是否需要触发算法
     */
    private boolean shouldTriggerAlgorithm() {
        List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("is_show", 1)
                .eq("is_del", 0));
        return goodsList.isEmpty();
    }

    /**
     * 执行算法
     */
    private void executeAlgorithmAsync() {
        try {
            //查询今天所有的订单
            List<HtOrder> orderList = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                    .like("created_at", DateUtils.getDate()).eq("order_status",1));
            for(HtOrder htOrder:orderList){
                if(htOrder.getOrderType()==1){
                }
                if(htOrder.getOrderType()==3){
                    htOrder.setOrderStatus();
                }
                if(htOrder.getOrderType()==0){
                }
                htOrderMapper.updateById(htOrder);
                createOrderUtils.createOrderRecord(htOrder);
            }
        } catch (Exception e) {
            log.error("执行算法异常", e);
        }
    }
}

